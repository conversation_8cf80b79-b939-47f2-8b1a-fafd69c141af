/**
 * 注册页面Content Script
 * 处理Augment Code注册页面的自动化操作
 */

// 内联常量定义，避免ES6模块导入问题
const RegMessageType = {
  START_AUTO_REGISTRATION: 'startAutoRegistration',
  FILL_EMAIL: 'fillEmail',
  SEND_VERIFICATION_CODE: 'sendVerificationCode',
  FILL_VERIFICATION_CODE: 'fillVerificationCode',
  SUBMIT_REGISTRATION: 'submitRegistration',
  CHECK_REGISTRATION_STATUS: 'checkRegistrationStatus',
  GET_VERIFICATION_CODE: 'getVerificationCode',
  MONITOR_EMAIL: 'monitorEmail',
  STOP_MONITORING: 'stopMonitoring'
} as const;

class RegistrationContentScript {
  private isReady: boolean = false;
  private currentEmail: string = '';
  private autoMonitorEnabled: boolean = false;
  private statusUI: HTMLElement | null = null;
  private isAutomationRunning: boolean = false;
  private currentStatus: string = '准备就绪';
  private currentPageState: 'email' | 'verification' | 'password' | 'terms' | 'unknown' = 'unknown';
  private monitoringTimer: number | null = null;
  private globalTimeoutTimer: number | null = null;
  
  constructor() {
    this.initialize();
    this.setupMessageListener();
  }
  
  private async initialize(): Promise<void> {
    console.log('🚀 RegistrationContentScript 开始初始化...');
    console.log('当前页面URL:', window.location.href);
    console.log('页面标题:', document.title);

    console.log('🎯 脚本已成功注入到域名:', window.location.hostname);

    // 检测并显示无痕模式状态
    this.checkAndDisplayIncognitoStatus();

    // 等待页面完全加载
    if (document.readyState === 'loading') {
      console.log('等待页面加载完成...');
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    console.log('页面已加载，开始分析页面类型...');

    // 检测页面类型
    const pageState = this.detectCurrentPageState();

    // 如果是验证码页面，进入验证码专用模式
    if (pageState === 'verification') {
      console.log('🔍 检测到验证码页面，进入验证码专用模式');
      this.currentPageState = 'verification';
      this.enterVerificationMode();
      this.isReady = true;
      return;
    }

    // 如果是条款同意页面，直接处理条款同意
    if (pageState === 'terms') {
      console.log('📋 检测到条款同意页面，准备自动处理');
      this.currentPageState = 'terms';
      this.isReady = true;

      // 创建状态指示器UI
      this.createStatusUI();

      // 延迟一下让页面完全加载，然后自动处理条款同意
      setTimeout(() => {
        this.handleTermsAgreementPage();
      }, 2000);
      return;
    }

    // 其他页面执行正常初始化
    await this.detectAndHandlePageType();
    this.updatePageState();
    this.isReady = true;
    console.log('✅ 登录/注册页面Content Script已就绪');

    // 创建状态指示器UI
    this.createStatusUI();
  }



  /**
   * 检测当前代理状态
   */
  private async checkProxyStatus(): Promise<void> {
    try {
      console.log('🔍 ==================== 代理状态检测 ====================');

      // 1. 检测当前IP地址
      await this.detectCurrentIP();

      // 2. 获取存储的代理信息
      await this.getStoredProxyInfo();

      // 3. 检测Chrome代理设置
      await this.getChromeProxySettings();

      console.log('🔍 ==================== 代理状态检测完成 ====================');
    } catch (error) {
      console.error('❌ 代理状态检测失败:', error);
    }
  }

  /**
   * 检测当前IP地址
   */
  private async detectCurrentIP(): Promise<void> {
    try {
      console.log('🌐 检测当前IP地址...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('https://httpbin.org/ip', {
        signal: controller.signal,
        method: 'GET'
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const result = await response.json();
        console.log('🌐 当前IP地址:', result.origin);
        console.log('🌐 IP检测成功，响应时间:', Date.now());
      } else {
        console.warn('⚠️ IP检测失败:', response.status, response.statusText);
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('⏰ IP检测超时');
        } else {
          console.warn('⚠️ IP检测失败:', error.message);
        }
      } else {
        console.warn('⚠️ IP检测失败:', String(error));
      }
    }
  }

  /**
   * 获取存储的代理信息
   */
  private async getStoredProxyInfo(): Promise<void> {
    try {
      console.log('💾 获取存储的代理信息...');

      const result = await chrome.storage.local.get(['currentProxy']);

      if (result.currentProxy) {
        const proxy = result.currentProxy;
        console.log('💾 存储的代理信息:');
        console.log('  - 主机:', proxy.host);
        console.log('  - 端口:', proxy.port);
        console.log('  - 类型:', proxy.type);
        console.log('  - 协议:', proxy.protocol);
        console.log('  - 用户名:', proxy.username || '无');
        console.log('  - 时间戳:', new Date(proxy.timestamp).toLocaleString());

        const age = Date.now() - proxy.timestamp;
        console.log('  - 代理年龄:', Math.round(age / 1000), '秒');
      } else {
        console.log('💾 未找到存储的代理信息');
      }
    } catch (error) {
      console.error('❌ 获取代理信息失败:', error);
    }
  }

  /**
   * 获取Chrome代理设置
   */
  private async getChromeProxySettings(): Promise<void> {
    try {
      console.log('🔧 获取Chrome代理设置...');

      // 通过background script获取代理设置
      const response = await chrome.runtime.sendMessage({
        action: 'getProxySettings'
      });

      if (response && response.success) {
        console.log('🔧 Chrome代理设置:', response.data);
      } else {
        console.log('🔧 无法获取Chrome代理设置:', response?.error || '未知错误');
      }
    } catch (error) {
      console.error('❌ 获取Chrome代理设置失败:', error);
    }
  }



  /**
   * 启动全局20秒超时监控
   */
  private startGlobalTimeout(): void {
    // 清除之前的定时器
    if (this.globalTimeoutTimer) {
      clearTimeout(this.globalTimeoutTimer);
    }



    // 设置20秒超时
    this.globalTimeoutTimer = window.setTimeout(async () => {
      console.log('🚨 ==================== 全局20秒超时触发 ====================');
      console.log('🚨 当前URL:', window.location.href);
      console.log('🚨 当前页面状态:', this.currentPageState);
      console.log('🚨 当前时间:', new Date().toLocaleString());

      await this.handleGlobalTimeout();
    }, 20000);

    console.log('⏰ 全局20秒超时监控已启动');
  }



  /**
   * 停止全局超时监控
   */
  private stopGlobalTimeout(): void {
    if (this.globalTimeoutTimer) {
      clearTimeout(this.globalTimeoutTimer);
      this.globalTimeoutTimer = null;
      console.log('⏰ 全局超时监控已停止');
    }
  }

  /**
   * 处理全局超时
   */
  private async handleGlobalTimeout(): Promise<void> {
    try {
      console.log('🚨 执行全局超时处理...');

      // 1. 清空浏览器数据
      console.log('🧹 开始清空浏览器数据...');
      try {
        await this.clearBrowserTraces();
        console.log('✅ 浏览器数据清理完成');
      } catch (error) {
        console.error('❌ 浏览器数据清理失败:', error);
      }

      // 2. 切换代理
      console.log('🔄 开始切换代理...');
      try {
        await this.switchProxy();
        console.log('✅ 代理切换完成');
      } catch (error) {
        console.error('❌ 代理切换失败:', error);
      }

      // 3. 等待清理和代理切换生效
      console.log('⏳ 等待清理和代理设置生效...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 4. 跳转到app页面重新开始
      console.log('🔄 跳转到app页面重新开始...');
      window.location.href = 'https://app.augmentcode.com/';

      console.log('🚨 ==================== 全局超时处理完成 ====================');
    } catch (error) {
      console.error('❌ 全局超时处理失败:', error);
      // 即使处理失败，也尝试跳转重新开始
      window.location.href = 'https://app.augmentcode.com/';
    }
  }

  /**
   * 检测页面类型并处理
   */
  private async detectAndHandlePageType(): Promise<void> {
    console.log('🔍 开始检测页面类型...');
    console.log('当前URL:', window.location.href);

    // 启动全局20秒超时监控
    this.startGlobalTimeout();

    // 首先检测当前代理状态
    await this.checkProxyStatus();

    // 等待页面完全加载
    await this.waitForPageLoad();

    // 🎯 检测是否为注册完成页面（优先级最高）
    if (this.isRegistrationCompletePage()) {
      console.log('🎉 检测到注册完成页面');
      // 重置全局超时计时器
      this.startGlobalTimeout();
      await this.handleRegistrationComplete();
      return;
    }

    // 检测是否为注册页面
    if (this.isRegistrationPage()) {
      console.log('✅ 检测到注册页面');
      // 重置全局超时计时器
      this.startGlobalTimeout();
      await this.waitForRegistrationForm();
      return;
    }

    // 检测是否为登录页面
    if (this.isLoginPage()) {
      console.log('🔄 检测到登录页面，尝试切换到注册模式');
      await this.switchToRegistrationMode();
      return;
    }

    // 通用处理 - 等待表单加载
    console.log('⚠️ 未明确识别页面类型，使用通用检测');
    await this.waitForAnyForm();
  }

  /**
   * 等待页面完全加载
   */
  private async waitForPageLoad(): Promise<void> {
    const maxWait = 10000; // 10秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWait) {
      // 检查页面是否有基本的表单元素
      const hasForm = document.querySelector('form') ||
                     document.querySelector('input[type="email"]') ||
                     document.querySelector('input[type="text"]');

      if (hasForm) {
        console.log('📋 页面表单元素已加载');
        return;
      }

      await this.sleep(200);
    }

    console.log('⏰ 页面加载等待超时，继续执行');
  }

  /**
   * 检测是否为注册完成页面
   */
  private isRegistrationCompletePage(): boolean {
    const url = window.location.href.toLowerCase();
    console.log('🔍 检查是否为注册完成页面，当前URL:', url);

    // 检查是否为订阅页面（表示注册成功）
    if (url.includes('/account/subscription')) {
      console.log('✅ 检测到订阅页面，注册已完成');
      return true;
    }

    // 也可以检查其他可能的成功页面
    const successUrls = [
      '/account/subscription',
      '/dashboard',
      '/account/profile',
      '/account'
    ];

    for (const successUrl of successUrls) {
      if (url.includes(successUrl)) {
        console.log(`✅ 检测到成功页面: ${successUrl}`);
        return true;
      }
    }

    return false;
  }

  /**
   * 检测是否为注册页面
   */
  private isRegistrationPage(): boolean {
    const url = window.location.href.toLowerCase();
    const title = document.title.toLowerCase();
    const bodyText = document.body.textContent?.toLowerCase() || '';

    // URL检测
    if (url.includes('signup') || url.includes('register')) {
      return true;
    }

    // 页面内容检测
    const registrationKeywords = ['sign up', 'create account', '注册', '创建账户', 'register'];
    return registrationKeywords.some(keyword =>
      title.includes(keyword) || bodyText.includes(keyword)
    );
  }

  /**
   * 检测是否为登录页面
   */
  private isLoginPage(): boolean {
    const url = window.location.href.toLowerCase();
    const title = document.title.toLowerCase();
    const bodyText = document.body.textContent?.toLowerCase() || '';

    // URL检测
    if (url.includes('login') || url.includes('signin')) {
      return true;
    }

    // 页面内容检测
    const loginKeywords = ['sign in', 'log in', '登录', 'login'];
    return loginKeywords.some(keyword =>
      title.includes(keyword) || bodyText.includes(keyword)
    );
  }

  /**
   * 切换到注册模式
   */
  private async switchToRegistrationMode(): Promise<void> {
    console.log('🔄 尝试切换到注册模式...');

    // 查找注册链接或按钮
    const signupSelectors = [
      'a[href*="signup"]',
      'a[href*="register"]',
      'button[data-action*="signup"]',
      'button[data-action*="register"]',
      'a:contains("Sign up")',
      'a:contains("注册")',
      'button:contains("Sign up")',
      'button:contains("注册")'
    ];

    for (const selector of signupSelectors) {
      try {
        const element = document.querySelector(selector) as HTMLElement;
        if (element && element.offsetParent !== null) {
          console.log(`🎯 找到注册链接: ${selector}`);
          await this.simulateClick(element);

          // 等待页面切换
          await this.sleep(2000);
          await this.waitForRegistrationForm();
          return;
        }
      } catch (error) {
        console.log(`❌ 选择器失败: ${selector}`, error);
      }
    }

    console.log('⚠️ 未找到注册链接，尝试通用表单处理');
    await this.waitForAnyForm();
  }

  /**
   * 等待任何表单加载（通用处理）
   */
  private async waitForAnyForm(): Promise<void> {
    const maxWait = 15000; // 15秒
    const startTime = Date.now();

    console.log('⏳ 等待表单元素加载...');

    while (Date.now() - startTime < maxWait) {
      // 检查邮箱输入框
      const emailInput = this.findEmailInput();
      if (emailInput) {
        console.log('✅ 找到邮箱输入框');
        return;
      }

      // 检查任何输入框
      const anyInput = document.querySelector('input[type="text"], input[type="email"]');
      if (anyInput) {
        console.log('✅ 找到输入框');
        return;
      }

      await this.sleep(500);
    }

    console.log('⚠️ 表单加载超时，但继续执行');
  }

  /**
   * 创建状态指示器UI
   */
  private createStatusUI(): void {
    // 移除现有的状态UI（如果存在）
    if (this.statusUI) {
      this.statusUI.remove();
    }

    // 创建主容器
    this.statusUI = document.createElement('div');
    this.statusUI.id = 'auto-register-status';
    this.statusUI.innerHTML = `
      <div class="status-header">
        <span class="status-title">🤖 自动注册助手</span>
        <button class="status-minimize" title="最小化">−</button>
      </div>
      <div class="status-content">
        <div class="status-indicator">
          <span class="status-dot"></span>
          <span class="status-text">${this.currentStatus}</span>
        </div>
        <div class="status-controls">
          <button class="control-btn start-btn" ${this.isAutomationRunning ? 'disabled' : ''}>
            ${this.isAutomationRunning ? '运行中...' : '开始注册'}
          </button>
          <button class="control-btn stop-btn" ${!this.isAutomationRunning ? 'disabled' : ''}>
            停止
          </button>
        </div>
        <div class="status-records">
          <button class="control-btn records-btn" title="查看所有注册成功记录">
            📋 查看记录
          </button>
          <button class="control-btn export-btn" title="导出注册记录为TXT文件">
            📤 导出记录
          </button>
        </div>
        <div class="status-info">
          <div class="info-item">
            <span class="info-label">当前邮箱:</span>
            <span class="info-value">${this.currentEmail || '未生成'}</span>
          </div>
        </div>
      </div>
    `;

    // 添加样式
    this.addStatusUIStyles();

    // 添加事件监听器
    this.setupStatusUIEvents();

    // 添加到页面
    document.body.appendChild(this.statusUI);

    console.log('✅ 状态指示器UI已创建');
  }

  /**
   * 添加状态UI样式
   */
  private addStatusUIStyles(): void {
    const existingStyle = document.getElementById('auto-register-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    const style = document.createElement('style');
    style.id = 'auto-register-styles';
    style.textContent = `
      #auto-register-status {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 280px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        z-index: 10000;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      }

      #auto-register-status.minimized .status-content {
        display: none;
      }

      .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        cursor: move;
      }

      .status-title {
        font-weight: 600;
        font-size: 15px;
      }

      .status-minimize {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.2s;
      }

      .status-minimize:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .status-content {
        padding: 16px;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #4ade80;
        margin-right: 8px;
        animation: pulse 2s infinite;
      }

      .status-dot.running {
        background: #fbbf24;
      }

      .status-dot.error {
        background: #ef4444;
      }

      .status-dot.success {
        background: #10b981;
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      .status-text {
        font-size: 13px;
        flex: 1;
      }

      .status-controls {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }

      .control-btn {
        flex: 1;
        padding: 8px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }

      .start-btn {
        background: #10b981;
        color: white;
      }

      .start-btn:hover:not(:disabled) {
        background: #059669;
      }

      .stop-btn {
        background: #ef4444;
        color: white;
      }

      .stop-btn:hover:not(:disabled) {
        background: #dc2626;
      }

      .control-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .status-records {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }

      .records-btn {
        background: #3b82f6;
        color: white;
        font-size: 11px;
      }

      .records-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .export-btn {
        background: #8b5cf6;
        color: white;
        font-size: 11px;
      }

      .export-btn:hover:not(:disabled) {
        background: #7c3aed;
      }

      .status-info {
        font-size: 12px;
      }

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
      }

      .info-label {
        opacity: 0.8;
      }

      .info-value {
        font-weight: 500;
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 设置状态UI事件监听器
   */
  private setupStatusUIEvents(): void {
    if (!this.statusUI) return;

    // 最小化/展开按钮
    const minimizeBtn = this.statusUI.querySelector('.status-minimize');
    minimizeBtn?.addEventListener('click', () => {
      this.statusUI?.classList.toggle('minimized');
      const btn = minimizeBtn as HTMLButtonElement;
      btn.textContent = this.statusUI?.classList.contains('minimized') ? '+' : '−';
    });

    // 开始按钮
    const startBtn = this.statusUI.querySelector('.start-btn');
    startBtn?.addEventListener('click', async () => {
      if (!this.isAutomationRunning) {
        // 根据当前页面状态决定启动什么功能
        this.updatePageState();
        if (this.currentPageState === 'verification') {
          // 在验证码页面启动验证码监控
          this.isAutomationRunning = true;
          this.updateVerificationModeUI();
          await this.startVerificationCodeMonitoring();
        } else {
          // 在注册页面启动注册流程
          await this.startManualAutomation();
        }
      }
    });

    // 停止按钮
    const stopBtn = this.statusUI.querySelector('.stop-btn');
    stopBtn?.addEventListener('click', () => {
      if (this.isAutomationRunning) {
        this.stopAutomation();
      }
    });

    // 查看记录按钮
    const recordsBtn = this.statusUI.querySelector('.records-btn');
    recordsBtn?.addEventListener('click', async () => {
      await this.showRegistrationRecords();
    });

    // 导出记录按钮
    const exportBtn = this.statusUI.querySelector('.export-btn');
    exportBtn?.addEventListener('click', async () => {
      await this.exportRegistrationRecords();
    });

    // 拖拽功能
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    const header = this.statusUI.querySelector('.status-header');
    header?.addEventListener('mousedown', (e) => {
      const mouseEvent = e as MouseEvent;
      isDragging = true;
      const rect = this.statusUI!.getBoundingClientRect();
      dragOffset.x = mouseEvent.clientX - rect.left;
      dragOffset.y = mouseEvent.clientY - rect.top;
      document.addEventListener('mousemove', handleDrag);
      document.addEventListener('mouseup', handleDragEnd);
    });

    const handleDrag = (e: MouseEvent) => {
      if (!isDragging || !this.statusUI) return;
      const x = e.clientX - dragOffset.x;
      const y = e.clientY - dragOffset.y;
      this.statusUI.style.left = Math.max(0, Math.min(window.innerWidth - this.statusUI.offsetWidth, x)) + 'px';
      this.statusUI.style.top = Math.max(0, Math.min(window.innerHeight - this.statusUI.offsetHeight, y)) + 'px';
      this.statusUI.style.right = 'auto';
    };

    const handleDragEnd = () => {
      isDragging = false;
      document.removeEventListener('mousemove', handleDrag);
      document.removeEventListener('mouseup', handleDragEnd);
    };
  }

  /**
   * 更新状态指示器
   */
  private updateStatus(status: string, type: 'ready' | 'running' | 'error' | 'success' = 'ready'): void {
    this.currentStatus = status;

    if (!this.statusUI) return;

    const statusText = this.statusUI.querySelector('.status-text');
    const statusDot = this.statusUI.querySelector('.status-dot');
    const startBtn = this.statusUI.querySelector('.start-btn') as HTMLButtonElement;
    const stopBtn = this.statusUI.querySelector('.stop-btn') as HTMLButtonElement;

    if (statusText) statusText.textContent = status;

    if (statusDot) {
      statusDot.className = `status-dot ${type}`;
    }

    // 更新按钮状态
    if (startBtn && stopBtn) {
      if (type === 'running') {
        startBtn.disabled = true;
        startBtn.textContent = '运行中...';
        stopBtn.disabled = false;
      } else {
        startBtn.disabled = false;
        startBtn.textContent = '开始注册';
        stopBtn.disabled = true;
      }
    }

    console.log(`📊 状态更新: ${status} (${type})`);
  }

  /**
   * 更新邮箱显示
   */
  private updateEmailDisplay(email: string): void {
    this.currentEmail = email;

    if (!this.statusUI) return;

    const emailValue = this.statusUI.querySelector('.info-value');
    if (emailValue) {
      emailValue.textContent = email || '未生成';
    }
  }

  /**
   * 确保邮箱地址正确显示（从页面或存储中获取）
   */
  private async ensureEmailDisplay(): Promise<void> {
    try {
      // 如果已经有邮箱地址，直接返回
      if (this.currentEmail && this.currentEmail !== '未生成') {
        console.log('✅ 当前邮箱地址已存在:', this.currentEmail);
        this.updateEmailDisplay(this.currentEmail);
        return;
      }

      // 尝试从页面HTML中提取邮箱地址
      console.log('📧 尝试从页面提取邮箱地址...');
      const pageEmail = this.extractEmailFromPage();
      if (pageEmail) {
        console.log('✅ 从页面提取到邮箱地址:', pageEmail);
        this.updateEmailDisplay(pageEmail);
        return;
      }

      // 尝试从Chrome存储中获取邮箱地址
      console.log('📧 尝试从存储中获取邮箱地址...');
      const result = await chrome.storage.local.get(['currentEmail']);
      if (result.currentEmail) {
        console.log('✅ 从存储中获取到邮箱地址:', result.currentEmail);
        this.updateEmailDisplay(result.currentEmail);
        return;
      }

      console.log('⚠️ 未能获取到邮箱地址，保持默认显示');
    } catch (error) {
      console.error('❌ 获取邮箱地址失败:', error);
    }
  }

  /**
   * 手动开始自动化
   */
  private async startManualAutomation(): Promise<void> {
    try {
      this.isAutomationRunning = true;
      this.updateStatus('正在生成邮箱...', 'running');

      // 请求background script生成邮箱并开始注册
      const response = await chrome.runtime.sendMessage({
        type: 'startRegistration'
      });

      if (response && response.success) {
        console.log('✅ 手动启动注册成功');
      } else {
        throw new Error(response?.error || '启动失败');
      }

    } catch (error) {
      console.error('❌ 手动启动失败:', error);
      this.updateStatus('启动失败: ' + (error as Error).message, 'error');
      this.isAutomationRunning = false;
    }
  }

  /**
   * 停止自动化
   */
  private stopAutomation(): void {
    this.isAutomationRunning = false;
    this.updateStatus('已停止', 'ready');
    console.log('🛑 自动化已停止');
  }

  private setupMessageListener(): void {
    // 移除导航调试监听器，避免干扰验证码页面
    // this.setupNavigationDebugListeners();

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
  }

  /**
   * 设置导航调试监听器
   */
  // @ts-ignore - 备用方法，暂时未使用
  private setupNavigationDebugListeners(): void {
    console.log('🔧 设置页面导航调试监听器...');

    // 监听页面卸载事件
    window.addEventListener('beforeunload', (event) => {
      console.log('🚨 ==================== 页面即将卸载 ====================');
      console.log('🚨 beforeunload事件触发');
      console.log('🚨 当前URL:', window.location.href);
      console.log('🚨 事件详情:', event);

      // 获取调用栈
      console.trace('🚨 页面卸载调用栈');
    });

    window.addEventListener('unload', (event) => {
      console.log('🚨 ==================== 页面正在卸载 ====================');
      console.log('🚨 unload事件触发');
      console.log('🚨 当前URL:', window.location.href);
      console.log('🚨 事件详情:', event);
    });

    // 监听页面显示事件
    window.addEventListener('pageshow', (event) => {
      console.log('📄 ==================== 页面显示事件 ====================');
      console.log('📄 pageshow事件触发');
      console.log('📄 是否从缓存加载:', event.persisted);
      console.log('📄 当前URL:', window.location.href);
    });

    // 监听页面隐藏事件
    window.addEventListener('pagehide', (event) => {
      console.log('📄 ==================== 页面隐藏事件 ====================');
      console.log('📄 pagehide事件触发');
      console.log('📄 是否进入缓存:', event.persisted);
      console.log('📄 当前URL:', window.location.href);
    });

    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', (event) => {
      console.log('🔄 ==================== 浏览器导航事件 ====================');
      console.log('🔄 popstate事件触发');
      console.log('🔄 当前URL:', window.location.href);
      console.log('🔄 状态对象:', event.state);
      console.trace('🔄 popstate调用栈');
    });

    // 监听hashchange事件
    window.addEventListener('hashchange', (event) => {
      console.log('🔗 ==================== URL哈希变化 ====================');
      console.log('🔗 hashchange事件触发');
      console.log('🔗 旧URL:', event.oldURL);
      console.log('🔗 新URL:', event.newURL);
    });

    console.log('✅ 页面导航调试监听器设置完成');

    // 设置页面状态持续监控
    this.setupPageStateMonitoring();
  }

  /**
   * 设置页面状态持续监控（暂时禁用以排查问题）
   */
  private setupPageStateMonitoring(): void {
    console.log('🔄 页面状态监控已暂时禁用（排查干扰问题）');

    // 暂时注释掉持续监控，避免干扰页面
    /*
    setInterval(() => {
      const oldState = this.currentPageState;
      this.updatePageState();

      // 如果检测到意外回到邮箱页面，记录详细信息
      if (oldState === 'verification' && this.currentPageState === 'email') {
        console.log('🚨 ==================== 检测到页面回退 ====================');
        console.log('🚨 从验证码页面回退到邮箱页面');
        console.log('🚨 当前URL:', window.location.href);
        console.log('🚨 页面标题:', document.title);
        console.trace('🚨 页面回退时的调用栈');

        this.updateStatus('检测到页面回退', 'error');
        this.stopAllTimers();
        this.isAutomationRunning = false;
      }
    }, 2000);
    */
  }
  
  private async handleMessage(message: any, _sender: any, sendResponse: Function): Promise<void> {
    try {
      // 在验证码页面只允许处理验证码相关的消息
      if (this.currentPageState === 'verification') {
        const allowedMessages = ['fillVerificationCode', 'getVerificationCode', 'monitorEmail'];
        if (!allowedMessages.includes(message.type)) {
          console.log('🔍 验证码专用模式：拒绝处理非验证码相关消息', message.type);
          sendResponse({
            success: false,
            error: '当前在验证码页面，只允许验证码相关操作'
          });
          return;
        }
        console.log('🔍 验证码专用模式：允许处理验证码相关消息', message.type);
      }

      switch (message.type) {
        case 'startAutoRegistration':
          const result = await this.startAutoRegistration(message.data);
          sendResponse(result);
          break;
          
        case 'fillEmail':
          const fillResult = await this.fillEmailField(message.data.email);
          sendResponse(fillResult);
          break;
          
        case 'fillVerificationCode':
          const codeResult = await this.fillVerificationCode(message.data.code);
          sendResponse(codeResult);
          break;
          
        case 'submitRegistration':
          const submitResult = await this.submitRegistrationForm();
          sendResponse(submitResult);
          break;
          
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Content Script处理消息失败:', error);
      sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
    }
  }
  
  /**
   * 等待注册表单加载完成
   */
  private async waitForRegistrationForm(): Promise<void> {
    const maxWait = 30000; // 30秒超时
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWait) {
      // 检查关键元素是否存在
      const emailInput = this.findEmailInput();
      const submitButton = this.findSubmitButton();
      
      if (emailInput && submitButton) {
        console.log('注册表单加载完成');
        return;
      }
      
      await this.sleep(500);
    }
    
    throw new Error('注册表单加载超时');
  }
  
  /**
   * 开始自动注册流程
   */
  private async startAutoRegistration(data: { email: string, autoMonitor: boolean }): Promise<any> {
    try {
      console.log('🚀 开始自动注册流程，接收到数据:', data);
      this.isAutomationRunning = true;
      this.updateStatus('开始自动注册...', 'running');

      // 验证邮箱参数
      if (!data || !data.email) {
        throw new Error('邮箱参数缺失或无效');
      }

      if (typeof data.email !== 'string' || data.email.trim() === '') {
        throw new Error('邮箱地址无效: ' + data.email);
      }

      console.log('📧 使用邮箱地址:', data.email);

      this.currentEmail = data.email;
      this.autoMonitorEnabled = data.autoMonitor || false;
      this.updateEmailDisplay(data.email);
      
      // 步骤1: 填充邮箱地址
      this.updateStatus('正在填充邮箱地址...', 'running');
      const fillResult = await this.fillEmailField(data.email);
      if (!fillResult.success) {
        throw new Error('填充邮箱地址失败: ' + fillResult.error);
      }

      // 步骤2: 点击发送验证码按钮
      this.updateStatus('正在点击继续按钮...', 'running');
      const sendResult = await this.clickSendVerificationCode();
      if (!sendResult.success) {
        throw new Error('点击继续按钮失败: ' + sendResult.error);
      }
      
      // 步骤3: 等待页面跳转到验证码页面，然后启动验证码监控
      console.log('✅ 邮箱填充和按钮点击完成，等待页面跳转...');
      this.updateStatus('等待跳转到验证码页面...', 'running');

      // 重置全局超时计时器
      this.startGlobalTimeout();

      // 等待页面跳转到验证码页面，然后启动验证码监控
      this.monitoringTimer = window.setTimeout(async () => {
        console.log('🔍 检查页面是否已跳转到验证码页面...');
        this.updatePageState();

        if (this.currentPageState === 'verification') {
          console.log('✅ 已进入验证码页面');
          if (this.autoMonitorEnabled) {
            console.log('🔍 自动监控已启用，开始验证码监控');
            this.updateStatus('正在监控验证码...', 'running');
            await this.startVerificationCodeMonitoring();
          } else {
            console.log('📝 自动监控未启用，等待手动输入');
            this.updateStatus('请手动输入验证码', 'ready');
            this.isAutomationRunning = false;
          }
        } else {
          console.log('❓ 页面未跳转到验证码页面，停止自动化');
          this.updateStatus('页面跳转异常，请手动操作', 'error');
          this.isAutomationRunning = false;
        }
      }, 3000);



      return {
        success: true,
        message: '自动注册流程已启动',
        nextStep: this.autoMonitorEnabled ? '正在监控验证码...' : '请手动输入验证码'
      };

    } catch (error) {
      console.error('自动注册流程失败:', error);
      this.isAutomationRunning = false;
      this.updateStatus('注册失败: ' + (error as Error).message, 'error');

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 填充邮箱字段
   */
  private async fillEmailField(email: string): Promise<any> {
    try {
      console.log('📧 开始填充邮箱字段，邮箱地址:', email);

      // 验证邮箱参数
      if (!email || typeof email !== 'string' || email.trim() === '') {
        throw new Error(`邮箱地址无效: ${email}`);
      }

      const emailInput = this.findEmailInput();
      if (!emailInput) {
        throw new Error('未找到邮箱输入框');
      }

      console.log('✅ 找到邮箱输入框:', emailInput);
      
      // 模拟人工输入（typeText方法会处理所有必要的事件）
      await this.typeText(emailInput, email);
      
      console.log('邮箱地址填充完成:', email);
      
      return {
        success: true,
        message: '邮箱地址填充成功'
      };
      
    } catch (error) {
      console.error('填充邮箱字段失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 点击发送验证码/继续按钮
   */
  private async clickSendVerificationCode(): Promise<any> {
    try {
      console.log('🔘 开始查找并点击继续/发送验证码按钮...');

      // 在点击前停止所有可能干扰的定时器和监控
      this.stopAllTimers();
      console.log('🛑 已停止所有定时器和监控');

      const sendButton = this.findSendCodeButton();
      if (!sendButton) {
        throw new Error('未找到继续/发送验证码按钮');
      }

      console.log('✅ 找到按钮:', sendButton);
      console.log('按钮文本:', sendButton.textContent?.trim() || (sendButton as HTMLInputElement).value?.trim());

      // 简单检查是否存在CF验证器（轻量级检测）
      const cfElement = this.findCloudflareVerificationCheckbox();
      if (cfElement) {
        console.log('🔒 检测到CF验证器，等待验证完成...');
        this.updateStatus('等待人机验证完成...', 'running');
        const cfVerified = await this.waitForCloudflareSuccess(15000); // 减少等待时间到15秒

        if (!cfVerified) {
          console.log('⚠️ CF验证超时，继续尝试点击');
          this.updateStatus('人机验证超时，尝试继续...', 'running');
        } else {
          console.log('✅ CF验证已完成');
          this.updateStatus('人机验证完成...', 'running');
        }
      } else {
        console.log('ℹ️ 未检测到CF验证器，直接处理按钮点击');
        this.updateStatus('准备点击按钮...', 'running');
      }

      // 检查按钮是否可点击
      if ((sendButton as HTMLButtonElement).disabled) {
        throw new Error('按钮不可点击（已禁用）');
      }

      // 模拟人工点击
      console.log('🖱️ 开始点击按钮...');
      await this.simulateClick(sendButton);

      console.log('✅ 按钮点击完成，让页面自然跳转');

      return {
        success: true,
        message: '继续按钮已点击，页面应该自然跳转'
      };

    } catch (error) {
      console.error('点击发送验证码按钮失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 开始验证码监控
   */
  // @ts-ignore - 方法被调用但TypeScript检查有误
  private async startVerificationCodeMonitoring(): Promise<void> {
    try {
      console.log('🔍 ==================== 开始验证码监控 ====================');

      // 步骤1: 确认页面状态
      this.updateStatus('检查页面状态...', 'running');
      this.updatePageState();
      if (this.currentPageState !== 'verification') {
        console.log('❌ 页面状态不是验证码页面，停止监控');
        console.log('❌ 当前页面状态:', this.currentPageState);
        this.updateStatus('页面状态错误，停止监控', 'error');
        this.isAutomationRunning = false;
        this.updateVerificationModeUI();
        return;
      }

      console.log('✅ 确认在验证码页面，准备启动监控...');

      // 步骤2: 从页面提取实际的邮箱地址
      this.updateStatus('提取页面邮箱地址...', 'running');
      const actualEmailAddress = this.extractEmailFromPage();

      if (!actualEmailAddress) {
        console.log('❌ 无法从页面提取邮箱地址，使用备用邮箱');
        this.updateStatus('无法提取邮箱地址，停止监控', 'error');
        this.isAutomationRunning = false;
        this.updateVerificationModeUI();
        return;
      }

      console.log('✅ 从页面提取到邮箱地址:', actualEmailAddress);

      // 更新当前邮箱地址为实际地址
      this.currentEmail = actualEmailAddress;
      this.updateEmailDisplay(actualEmailAddress);

      // 步骤3: 准备监控数据（使用实际邮箱地址）
      this.updateStatus('准备邮箱监控数据...', 'running');
      const emailInfo = {
        address: actualEmailAddress,
        counter: parseInt(actualEmailAddress.match(/aug(\d{4})/)?.[1] || '1'),
        provider: 'custom',
        createdAt: new Date().toISOString(),
        status: 'active'
      };

      // 步骤3: 启动邮箱监控
      this.updateStatus('正在启动邮箱监控系统...', 'running');
      console.log('📧 发送监控请求到background script...');

      const response = await chrome.runtime.sendMessage({
        type: RegMessageType.MONITOR_EMAIL,
        data: emailInfo
      });

      console.log('📧 邮箱监控响应:', response);

      // 步骤4: 处理监控结果
      if (response && response.success && response.verificationCode) {
        console.log('✅ 获取到验证码:', response.verificationCode);
        this.updateStatus('验证码获取成功！', 'success');

        // 等待一下让用户看到成功消息
        await this.sleep(1000);

        // 再次检查页面状态，确保仍在验证码页面
        this.updatePageState();
        if (this.currentPageState !== 'verification') {
          console.log('❌ 页面状态已变化，停止自动填充');
          this.updateStatus('页面状态变化，停止自动化', 'error');
          this.isAutomationRunning = false;
          this.updateVerificationModeUI();
          return;
        }

        this.updateStatus('正在自动填充验证码...', 'running');

        // 自动填充验证码
        const fillResult = await this.fillVerificationCode(response.verificationCode);
        if (fillResult.success) {
          this.updateStatus('✅ 验证码已填充，准备提交...', 'running');

          this.updateStatus('正在提交验证码...', 'running');

          // 自动点击提交按钮
          const submitResult = await this.submitVerificationForm();
          if (submitResult.success) {
            this.updateStatus('✅ 验证码已提交成功！', 'success');
            console.log('✅ 验证码填充并提交完成');

            // 等待页面跳转到条款同意页面
            this.updateStatus('等待页面跳转...', 'running');
            // 重置全局超时计时器
            this.startGlobalTimeout();
            this.waitForTermsPage();

          } else {
            // 特殊处理CSRF错误
            if (submitResult.isCSRFError) {
              this.updateStatus('❌ 安全检查失败，请刷新页面重试', 'error');
              console.log('❌ CSRF错误，建议用户刷新页面');

              // 显示用户友好的错误提示
              this.showCSRFErrorNotification();
            } else {
              this.updateStatus(`提交失败: ${submitResult.error}，请手动提交`, 'error');
              console.log('❌ 自动提交失败:', submitResult.error);
            }

            this.isAutomationRunning = false;
            this.updateVerificationModeUI();
          }
        } else {
          console.log('❌ 验证码填充失败:', fillResult.error);
          this.updateStatus(`验证码填充失败: ${fillResult.error}`, 'error');
          this.isAutomationRunning = false;
          this.updateVerificationModeUI();
        }
      } else if (response && response.success === false) {
        // 监控启动失败
        const errorMsg = response.error || '未知错误';
        console.log('❌ 邮箱监控启动失败:', errorMsg);
        this.updateStatus(`监控启动失败: ${errorMsg}`, 'error');
        this.isAutomationRunning = false;
        this.updateVerificationModeUI();

        // 显示详细的错误信息和建议
        this.showDetailedError(errorMsg);
      } else {
        // 监控成功但未获取到验证码
        console.log('❌ 监控超时，未获取到验证码');
        this.updateStatus('监控超时，未获取到验证码，请手动输入', 'ready');
        this.isAutomationRunning = false;
        this.updateVerificationModeUI();
      }
    } catch (error) {
      console.error('❌ 验证码监控失败:', error);
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.updateStatus(`验证码监控失败: ${errorMsg}`, 'error');
      this.isAutomationRunning = false;
      this.updateVerificationModeUI();

      // 显示详细的错误信息
      this.showDetailedError(errorMsg);
    }
  }

  /**
   * 显示CSRF错误通知
   */
  private showCSRFErrorNotification(): void {
    console.log('🚨 显示CSRF错误通知');

    // 创建错误通知元素
    const notification = document.createElement('div');
    notification.id = 'csrf-error-notification';
    notification.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: white;
      padding: 20px 30px;
      border-radius: 12px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 16px;
      font-weight: 500;
      z-index: 10001;
      max-width: 400px;
      text-align: center;
      border: 2px solid rgba(255, 255, 255, 0.2);
    `;

    notification.innerHTML = `
      <div style="margin-bottom: 15px; font-size: 24px;">⚠️</div>
      <div style="margin-bottom: 15px; font-weight: 600;">安全检查失败</div>
      <div style="margin-bottom: 20px; font-size: 14px; opacity: 0.9;">
        检测到CSRF安全错误，这通常是由于页面状态不匹配导致的。
      </div>
      <div style="font-size: 14px; opacity: 0.8;">
        建议：刷新页面后重新尝试注册流程
      </div>
    `;

    document.body.appendChild(notification);

    // 5秒后自动移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * 显示详细的错误信息和解决建议
   */
  private showDetailedError(errorMsg: string): void {
    console.log('🔍 显示详细错误信息:', errorMsg);

    let suggestions: string[] = [];

    if (errorMsg.includes('权限') || errorMsg.includes('permission')) {
      suggestions = [
        '请检查Chrome扩展权限设置',
        '尝试重新安装扩展',
        '确保浏览器允许扩展访问标签页'
      ];
    } else if (errorMsg.includes('登录') || errorMsg.includes('login')) {
      suggestions = [
        '请先手动登录QQ邮箱',
        '确保QQ邮箱保持登录状态',
        '检查是否需要验证码登录'
      ];
    } else if (errorMsg.includes('超时') || errorMsg.includes('timeout')) {
      suggestions = [
        '检查网络连接是否正常',
        '尝试手动打开QQ邮箱确认可访问',
        '等待网络状况改善后重试'
      ];
    } else {
      suggestions = [
        '检查网络连接状态',
        '确保QQ邮箱可以正常访问',
        '尝试重启浏览器后重试'
      ];
    }

    // 在控制台输出详细建议
    console.log('💡 解决建议:');
    suggestions.forEach((suggestion, index) => {
      console.log(`   ${index + 1}. ${suggestion}`);
    });
  }

  /**
   * 填充验证码
   */
  private async fillVerificationCode(code: string): Promise<any> {
    try {
      const codeInput = this.findVerificationCodeInput();
      if (!codeInput) {
        throw new Error('未找到验证码输入框');
      }
      
      // 模拟人工输入（typeText方法会处理所有必要的事件）
      await this.typeText(codeInput, code);
      
      console.log('验证码填充完成:', code);
      
      return {
        success: true,
        message: '验证码填充成功'
      };
      
    } catch (error) {
      console.error('填充验证码失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 提交注册表单
   */
  private async submitRegistrationForm(): Promise<any> {
    try {
      const submitButton = this.findSubmitButton();
      if (!submitButton) {
        throw new Error('未找到提交按钮');
      }

      // 检查按钮是否可点击
      if ((submitButton as HTMLButtonElement).disabled) {
        throw new Error('提交按钮不可点击');
      }

      // 模拟人工点击
      await this.simulateClick(submitButton);

      console.log('已提交注册表单');

      return {
        success: true,
        message: '注册表单已提交'
      };

    } catch (error) {
      console.error('提交注册表单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 提交验证码表单（专门用于验证码页面）
   */
  private async submitVerificationForm(): Promise<any> {
    try {
      console.log('🔍 ==================== 开始验证码表单提交 ====================');

      // 步骤1: 检查和处理CSRF token
      console.log('🔒 检查CSRF token...');
      const csrfResult = await this.ensureCSRFToken();
      if (!csrfResult.success) {
        console.warn('⚠️ CSRF token检查失败，但继续尝试提交:', csrfResult.error);
      }

      // 步骤2: 查找表单和提交按钮
      const form = this.findVerificationForm();
      const submitButton = this.findVerificationSubmitButton();

      if (!submitButton) {
        throw new Error('未找到验证码提交按钮');
      }

      console.log('✅ 找到验证码提交按钮:', submitButton);
      console.log('按钮文本:', submitButton.textContent?.trim());
      console.log('按钮类名:', submitButton.className);

      if (form) {
        console.log('✅ 找到验证码表单:', form);
        console.log('表单action:', form.action);
        console.log('表单method:', form.method);
      }

      // 步骤3: 检查按钮状态
      if ((submitButton as HTMLButtonElement).disabled) {
        throw new Error('验证码提交按钮不可点击');
      }

      // 步骤4: 短暂延迟确保表单准备就绪
      await this.sleep(500);

      // 步骤5: 确保表单验证通过
      if (form && !form.checkValidity()) {
        console.warn('⚠️ 表单验证未通过，但继续尝试提交');
      }

      // 步骤6: 模拟人工提交
      console.log('🖱️ 开始模拟人工提交验证码表单...');

      // 优先尝试表单提交（更安全）
      if (form) {
        console.log('📋 使用表单提交方式...');
        await this.submitFormDirectly(form, submitButton);
      } else {
        console.log('🖱️ 使用按钮点击方式...');
        await this.simulateClick(submitButton);
      }

      console.log('✅ 验证码表单提交命令已执行');

      return {
        success: true,
        message: '验证码表单已提交'
      };

    } catch (error) {
      console.error('❌ 提交验证码表单失败:', error);

      // 如果是CSRF错误，提供特殊处理
      if (error instanceof Error && error.message.toLowerCase().includes('csrf')) {
        return {
          success: false,
          error: 'CSRF安全检查失败，请刷新页面后重试',
          isCSRFError: true
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 查找邮箱输入框
   */
  private findEmailInput(): HTMLInputElement | null {
    const selectors = [
      // 标准邮箱输入框
      'input[type="email"]',
      'input[name*="email"]',
      'input[id*="email"]',
      'input[placeholder*="邮箱"]',
      'input[placeholder*="email"]',
      // Auth0常用选择器
      'input[name="username"]',
      'input[name="identifier"]',
      'input[id="username"]',
      'input[id="identifier"]',
      'input[placeholder*="Email"]',
      'input[placeholder*="Username"]',
      // 通用文本输入框（可能用于邮箱）
      'input[type="text"][placeholder*="@"]',
      'input[type="text"][name*="user"]'
    ];
    
    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLInputElement;
      if (element && element.offsetParent !== null) {
        return element;
      }
    }
    
    return null;
  }
  
  /**
   * 查找发送验证码/继续按钮
   */
  private findSendCodeButton(): HTMLElement | null {
    console.log('🔍 开始查找继续/发送验证码按钮...');

    // Auth0和通用按钮选择器
    const selectors = [
      // Auth0常用按钮选择器
      'button[type="submit"]',
      'button[data-action-button-primary="true"]',
      'button[class*="primary"]',
      'button[class*="continue"]',
      'button[class*="next"]',
      // 传统验证码按钮选择器
      'button[class*="send"]',
      'button[id*="send"]',
      'button[class*="code"]',
      'button[id*="code"]',
      // 通用提交按钮
      'input[type="submit"]',
      'button[role="button"]'
    ];

    // 先尝试CSS选择器
    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement;
      if (element && element.offsetParent !== null) {
        console.log(`✅ 通过选择器找到按钮: ${selector}`, element);
        return element;
      }
    }

    // 然后尝试文本匹配
    const buttons = document.querySelectorAll('button, [role="button"], input[type="submit"]');
    console.log(`🔍 找到 ${buttons.length} 个按钮，开始文本匹配...`);

    for (const button of Array.from(buttons)) {
      const text = button.textContent?.toLowerCase().trim() || '';
      const value = (button as HTMLInputElement).value?.toLowerCase().trim() || '';
      const buttonText = text || value;

      console.log(`🔍 检查按钮文本: "${buttonText}"`);

      // 优先匹配"继续"按钮（Auth0常用）
      if (buttonText.includes('继续') || buttonText.includes('continue')) {
        console.log('✅ 找到继续按钮:', button);
        return button as HTMLElement;
      }

      // 匹配其他可能的按钮文本
      if (buttonText.includes('发送') ||
          buttonText.includes('获取') ||
          buttonText.includes('send') ||
          buttonText.includes('code') ||
          buttonText.includes('next') ||
          buttonText.includes('submit') ||
          buttonText.includes('提交')) {
        console.log('✅ 找到匹配按钮:', button);
        return button as HTMLElement;
      }
    }

    console.log('❌ 未找到匹配的按钮');
    console.log('页面上的所有按钮:');
    buttons.forEach((btn, index) => {
      const text = btn.textContent?.trim() || (btn as HTMLInputElement).value?.trim() || '';
      console.log(`  ${index + 1}. "${text}" - ${btn.tagName} ${btn.className}`);
    });

    return null;
  }

  /**
   * 查找验证码输入框
   */
  private findVerificationCodeInput(): HTMLInputElement | null {
    const selectors = [
      'input[name*="code"]',
      'input[id*="code"]',
      'input[name*="verification"]',
      'input[id*="verification"]',
      'input[placeholder*="验证码"]',
      'input[placeholder*="code"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLInputElement;
      if (element && element.offsetParent !== null) {
        return element;
      }
    }

    return null;
  }

  /**
   * 查找提交按钮
   */
  private findSubmitButton(): HTMLElement | null {
    const selectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button[class*="submit"]',
      'button[id*="submit"]',
      'button[class*="register"]',
      'button[id*="register"]'
    ];

    // 先尝试CSS选择器
    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement;
      if (element && element.offsetParent !== null) {
        return element;
      }
    }

    // 然后尝试文本匹配
    const buttons = document.querySelectorAll('button, [role="button"]');
    for (const button of Array.from(buttons)) {
      const text = button.textContent?.toLowerCase() || '';
      if (text.includes('注册') || text.includes('提交') || text.includes('register') || text.includes('submit')) {
        return button as HTMLElement;
      }
    }

    return null;
  }

  /**
   * 查找验证码页面的提交按钮
   */
  private findVerificationSubmitButton(): HTMLElement | null {
    console.log('🔍 开始查找验证码页面的提交按钮...');

    // 根据您提供的按钮信息，查找具有特定类名的按钮
    const specificSelectors = [
      // 您提供的具体按钮选择器
      'button[type="submit"][name="action"][value="default"][data-action-button-primary="true"]',
      'button[data-action-button-primary="true"]',
      'button[name="action"][value="default"]',
      'button[type="submit"][name="action"]',
      // 包含特定类名的按钮
      'button.cdee2a1c5.c0acda5b0.c6536841f.c3e77fae7.cdbdc9258',
      'button[class*="cdee2a1c5"]',
      'button[class*="c0acda5b0"]',
      'button[class*="c6536841f"]'
    ];

    // 先尝试特定的CSS选择器
    for (const selector of specificSelectors) {
      try {
        const element = document.querySelector(selector) as HTMLElement;
        if (element && element.offsetParent !== null) {
          console.log(`✅ 通过选择器找到验证码提交按钮: ${selector}`, element);
          return element;
        }
      } catch (error) {
        console.log(`❌ 选择器失败: ${selector}`, error);
      }
    }

    // 通用提交按钮选择器
    const generalSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button[class*="submit"]',
      'button[id*="submit"]',
      'button[class*="primary"]',
      'button[role="button"]'
    ];

    for (const selector of generalSelectors) {
      const element = document.querySelector(selector) as HTMLElement;
      if (element && element.offsetParent !== null) {
        console.log(`✅ 通过通用选择器找到按钮: ${selector}`, element);
        return element;
      }
    }

    // 最后尝试文本匹配
    const buttons = document.querySelectorAll('button, [role="button"], input[type="submit"]');
    console.log(`🔍 找到 ${buttons.length} 个按钮，开始文本匹配...`);

    for (const button of Array.from(buttons)) {
      const text = button.textContent?.toLowerCase().trim() || '';
      const value = (button as HTMLInputElement).value?.toLowerCase().trim() || '';
      const buttonText = text || value;

      console.log(`🔍 检查按钮文本: "${buttonText}"`);

      // 匹配可能的提交按钮文本
      if (buttonText.includes('继续') ||
          buttonText.includes('提交') ||
          buttonText.includes('确认') ||
          buttonText.includes('完成') ||
          buttonText.includes('下一步') ||
          buttonText.includes('continue') ||
          buttonText.includes('submit') ||
          buttonText.includes('confirm') ||
          buttonText.includes('next') ||
          buttonText.includes('finish')) {
        console.log('✅ 找到匹配的验证码提交按钮:', button);
        return button as HTMLElement;
      }
    }

    console.log('❌ 未找到验证码提交按钮');
    console.log('页面上的所有按钮:');
    buttons.forEach((btn, index) => {
      const text = btn.textContent?.trim() || (btn as HTMLInputElement).value?.trim() || '';
      console.log(`  ${index + 1}. "${text}" - ${btn.tagName} ${btn.className}`);
    });

    return null;
  }

  /**
   * 从页面HTML中提取实际的邮箱地址
   */
  private extractEmailFromPage(): string | null {
    console.log('📧 ==================== 开始提取页面邮箱地址 ====================');

    try {
      // 方法1: 查找您提供的特定HTML结构
      const accountEmailElement = document.querySelector('.cmp-account-email');
      if (accountEmailElement) {
        const emailText = accountEmailElement.textContent || '';
        console.log('✅ 找到账户邮箱元素:', emailText);

        // 提取邮箱地址（去除 < > 符号）
        const emailMatch = emailText.match(/<([^>]+@[^>]+)>/);
        if (emailMatch && emailMatch[1]) {
          const extractedEmail = emailMatch[1].trim();
          console.log('✅ 成功提取邮箱地址:', extractedEmail);
          return extractedEmail;
        }
      }

      // 方法2: 查找其他可能的邮箱显示元素
      const emailSelectors = [
        '.cmp-account-email',
        '.account-email',
        '.user-email',
        '.email-display',
        '[class*="email"]',
        '[data-email]'
      ];

      for (const selector of emailSelectors) {
        const elements = document.querySelectorAll(selector);
        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];
          const text = element.textContent || element.getAttribute('data-email') || '';
          console.log(`🔍 检查元素 ${selector}:`, text);

          // 尝试提取邮箱地址
          const emailMatch = text.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
          if (emailMatch && emailMatch[1]) {
            const extractedEmail = emailMatch[1].trim();
            console.log('✅ 通过选择器提取到邮箱:', extractedEmail);
            return extractedEmail;
          }
        }
      }

      // 方法3: 在整个页面内容中搜索邮箱地址
      console.log('🔍 在页面内容中搜索邮箱地址...');
      const pageText = document.body.textContent || '';

      // 查找aug开头的邮箱地址（符合生成的邮箱格式）
      const augEmailMatch = pageText.match(/(aug\d{4}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (augEmailMatch && augEmailMatch[1]) {
        const extractedEmail = augEmailMatch[1].trim();
        console.log('✅ 在页面内容中找到aug邮箱:', extractedEmail);
        return extractedEmail;
      }

      // 方法4: 查找任何邮箱地址作为备用
      const anyEmailMatch = pageText.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (anyEmailMatch && anyEmailMatch[1]) {
        const extractedEmail = anyEmailMatch[1].trim();
        console.log('✅ 在页面内容中找到邮箱地址:', extractedEmail);
        return extractedEmail;
      }

      // 方法5: 检查URL参数中是否有邮箱信息
      const urlParams = new URLSearchParams(window.location.search);
      const paramKeys = ['email', 'user', 'account'];
      for (const key of paramKeys) {
        const value = urlParams.get(key);
        if (value && value.includes('@')) {
          const emailMatch = value.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
          if (emailMatch && emailMatch[1]) {
            const extractedEmail = emailMatch[1].trim();
            console.log('✅ 从URL参数中提取到邮箱:', extractedEmail);
            return extractedEmail;
          }
        }
      }

      console.log('❌ 未能从页面提取邮箱地址');
      console.log('页面内容预览:', pageText.substring(0, 500) + '...');

      return null;

    } catch (error) {
      console.error('❌ 提取邮箱地址时出错:', error);
      return null;
    }
  }

  /**
   * 查找验证码表单
   */
  private findVerificationForm(): HTMLFormElement | null {
    // 查找包含验证码输入框的表单
    const codeInput = this.findVerificationCodeInput();
    if (codeInput) {
      const form = codeInput.closest('form') as HTMLFormElement;
      if (form) {
        console.log('✅ 通过验证码输入框找到表单');
        return form;
      }
    }

    // 查找包含提交按钮的表单
    const submitButton = this.findVerificationSubmitButton();
    if (submitButton) {
      const form = submitButton.closest('form') as HTMLFormElement;
      if (form) {
        console.log('✅ 通过提交按钮找到表单');
        return form;
      }
    }

    // 查找页面上的第一个表单
    const forms = document.querySelectorAll('form');
    if (forms.length > 0) {
      console.log('✅ 使用页面上的第一个表单');
      return forms[0] as HTMLFormElement;
    }

    console.log('❌ 未找到验证码表单');
    return null;
  }

  /**
   * 确保CSRF token存在并有效
   */
  private async ensureCSRFToken(): Promise<{success: boolean, error?: string}> {
    try {
      console.log('🔒 开始检查CSRF token...');

      // 方法1: 查找隐藏的CSRF token输入字段
      const csrfInputs = document.querySelectorAll('input[name*="csrf"], input[name*="token"], input[name*="_token"]');
      if (csrfInputs.length > 0) {
        console.log('✅ 找到CSRF token输入字段:', csrfInputs.length);
        csrfInputs.forEach((input, index) => {
          const inputElement = input as HTMLInputElement;
          console.log(`  ${index + 1}. ${inputElement.name} = ${inputElement.value.substring(0, 20)}...`);
        });
        return { success: true };
      }

      // 方法2: 查找meta标签中的CSRF token
      const csrfMeta = document.querySelector('meta[name="csrf-token"], meta[name="_token"]') as HTMLMetaElement;
      if (csrfMeta && csrfMeta.content) {
        console.log('✅ 找到meta标签中的CSRF token:', csrfMeta.content.substring(0, 20) + '...');
        return { success: true };
      }

      // 方法3: 检查页面中是否有CSRF相关的JavaScript变量
      const pageContent = document.documentElement.innerHTML;
      if (pageContent.includes('csrf') || pageContent.includes('_token')) {
        console.log('✅ 页面内容中包含CSRF相关信息');
        return { success: true };
      }

      console.log('⚠️ 未找到明显的CSRF token，但这可能是正常的');
      return { success: false, error: '未找到CSRF token' };

    } catch (error) {
      console.error('❌ CSRF token检查失败:', error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  /**
   * 直接提交表单（更安全的方式）
   */
  private async submitFormDirectly(form: HTMLFormElement, submitButton: HTMLElement): Promise<void> {
    console.log('📋 ==================== 直接表单提交 ====================');

    try {
      // 确保所有必要的字段都已填充
      const inputs = form.querySelectorAll('input, select, textarea');
      console.log('📋 表单字段检查:');
      inputs.forEach((input, index) => {
        const inputElement = input as HTMLInputElement;
        console.log(`  ${index + 1}. ${inputElement.name || inputElement.id}: "${inputElement.value}" (${inputElement.type})`);
      });

      // 触发表单验证
      if (!form.checkValidity()) {
        console.warn('⚠️ 表单验证失败，尝试修复...');
        form.reportValidity();
      }

      // 先聚焦提交按钮
      submitButton.focus();
      await this.sleep(200);

      // 方法1: 尝试通过按钮提交（保持原有行为）
      console.log('📋 方法1: 通过按钮点击提交...');
      await this.simulateClick(submitButton);

      // 等待一下，如果上面的方法没有工作，尝试直接提交表单
      await this.sleep(500);

      // 方法2: 直接调用表单的submit方法（备用）
      console.log('📋 方法2: 直接调用form.submit()（备用）...');
      // 注意：直接调用submit()可能会跳过一些验证，所以作为备用方案
      // form.submit();

    } catch (error) {
      console.error('❌ 直接表单提交失败:', error);
      throw error;
    }
  }

  /**
   * 模拟人工输入文本
   */
  private async typeText(element: HTMLInputElement, text: string): Promise<void> {
    // 验证输入参数
    if (!text || typeof text !== 'string') {
      throw new Error(`无效的文本参数: ${text}`);
    }

    if (!element) {
      throw new Error('输入元素不存在');
    }

    console.log(`🔤 开始输入文本: "${text}"`);

    // 清空现有内容并聚焦
    element.value = '';
    element.focus();

    // 触发焦点事件
    element.dispatchEvent(new FocusEvent('focus', { bubbles: true }));

    // 等待一小段时间模拟人类反应
    await this.sleep(100 + Math.random() * 200);

    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const keyCode = char.charCodeAt(0);

      // 模拟完整的按键事件序列
      const keydownEvent = new KeyboardEvent('keydown', {
        key: char,
        code: this.getKeyCode(char),
        keyCode: keyCode,
        which: keyCode,
        bubbles: true,
        cancelable: true
      });
      element.dispatchEvent(keydownEvent);

      // 模拟按键按下事件
      const keypressEvent = new KeyboardEvent('keypress', {
        key: char,
        code: this.getKeyCode(char),
        keyCode: keyCode,
        which: keyCode,
        bubbles: true,
        cancelable: true
      });
      element.dispatchEvent(keypressEvent);

      // 更新值
      element.value += char;

      // 触发输入事件
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);

      // 模拟按键释放
      const keyupEvent = new KeyboardEvent('keyup', {
        key: char,
        code: this.getKeyCode(char),
        keyCode: keyCode,
        which: keyCode,
        bubbles: true,
        cancelable: true
      });
      element.dispatchEvent(keyupEvent);

      // 随机延迟，模拟人工输入速度 (80-200ms)
      await this.sleep(80 + Math.random() * 120);
    }

    // 输入完成后触发change和blur事件
    element.dispatchEvent(new Event('change', { bubbles: true }));

    // 等待一小段时间再失去焦点
    await this.sleep(100);
    element.dispatchEvent(new FocusEvent('blur', { bubbles: true }));

    console.log(`✅ 文本输入完成: "${text}"`);
  }

  /**
   * 获取字符对应的键码
   */
  private getKeyCode(char: string): string {
    if (char >= 'a' && char <= 'z') {
      return `Key${char.toUpperCase()}`;
    } else if (char >= 'A' && char <= 'Z') {
      return `Key${char}`;
    } else if (char >= '0' && char <= '9') {
      return `Digit${char}`;
    } else if (char === '@') {
      return 'Digit2';
    } else if (char === '.') {
      return 'Period';
    } else {
      return 'Unidentified';
    }
  }

  /**
   * 模拟人工点击（重新设计版）
   */
  private async simulateClick(element: HTMLElement): Promise<void> {
    console.log('🖱️ ==================== 开始点击分析 ====================');
    console.log('🖱️ 目标元素:', element);
    console.log('🖱️ 元素标签:', element.tagName);
    console.log('🖱️ 元素类名:', element.className);
    console.log('🖱️ 元素ID:', element.id);
    console.log('🖱️ 元素文本:', element.textContent?.trim());
    console.log('🖱️ 按钮类型:', (element as HTMLButtonElement).type);
    console.log('🖱️ 按钮禁用状态:', (element as HTMLButtonElement).disabled);

    // 检查表单状态
    const form = element.closest('form');
    if (form) {
      console.log('📋 ==================== 表单分析 ====================');
      console.log('📋 表单元素:', form);
      console.log('📋 表单action:', form.action);
      console.log('📋 表单method:', form.method);
      console.log('📋 表单验证状态:', form.checkValidity());

      // 检查所有输入字段
      const inputs = form.querySelectorAll('input, select, textarea');
      console.log('📋 表单字段数量:', inputs.length);
      inputs.forEach((input, index) => {
        const inputElement = input as HTMLInputElement;
        console.log(`📋 字段${index + 1}:`, {
          name: inputElement.name,
          type: inputElement.type,
          value: inputElement.value,
          required: inputElement.required,
          valid: inputElement.checkValidity()
        });
      });
    }

    // 记录页面状态
    const beforeState = {
      url: window.location.href,
      title: document.title,
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash
    };
    console.log('📄 ==================== 点击前页面状态 ====================');
    console.log('📄 完整URL:', beforeState.url);
    console.log('📄 页面标题:', beforeState.title);
    console.log('📄 路径:', beforeState.pathname);
    console.log('📄 查询参数:', beforeState.search);
    console.log('📄 锚点:', beforeState.hash);

    // 移除JavaScript错误监听，避免干扰页面导航

    try {
      // 确保元素可见
      if (element.offsetParent === null) {
        console.warn('⚠️ 元素不可见，滚动到视图');
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.sleep(500);
      }

      // 聚焦元素
      console.log('🎯 聚焦目标元素...');
      element.focus();
      await this.sleep(200);

      console.log('🖱️ ==================== 执行点击 ====================');
      console.log('🖱️ 使用完整的鼠标事件序列模拟真实点击...');

      // 获取元素的位置信息
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      console.log('🖱️ 元素位置:', { x: centerX, y: centerY, width: rect.width, height: rect.height });

      // 模拟完整的鼠标事件序列
      const mouseEventOptions = {
        bubbles: true,
        cancelable: true,
        clientX: centerX,
        clientY: centerY,
        button: 0,
        buttons: 1
      };

      // 1. 鼠标进入
      console.log('🖱️ 触发 mouseenter 事件...');
      element.dispatchEvent(new MouseEvent('mouseenter', mouseEventOptions));
      await this.sleep(50);

      // 2. 鼠标悬停
      console.log('🖱️ 触发 mouseover 事件...');
      element.dispatchEvent(new MouseEvent('mouseover', mouseEventOptions));
      await this.sleep(50);

      // 3. 鼠标按下
      console.log('🖱️ 触发 mousedown 事件...');
      element.dispatchEvent(new MouseEvent('mousedown', mouseEventOptions));
      await this.sleep(100);

      // 4. 聚焦事件
      console.log('🖱️ 触发 focus 事件...');
      element.focus();
      element.dispatchEvent(new FocusEvent('focus', { bubbles: true }));
      await this.sleep(50);

      // 5. 鼠标释放
      console.log('🖱️ 触发 mouseup 事件...');
      element.dispatchEvent(new MouseEvent('mouseup', mouseEventOptions));
      await this.sleep(50);

      // 6. 点击事件
      console.log('🖱️ 触发 click 事件...');
      element.dispatchEvent(new MouseEvent('click', mouseEventOptions));

      // 7. 备用的原生点击（以防自定义事件不够）
      console.log('🖱️ 执行原生 element.click()...');
      element.click();

      console.log('🖱️ 点击命令已执行，等待页面自然跳转...');

      // 简单等待，让页面自然跳转，不进行复杂监控
      await this.sleep(1000);

      console.log('✅ 点击完成，让页面自然跳转');

    } catch (error) {
      console.error('❌ 点击执行失败:', error);
      throw error;
    }

    console.log('🖱️ ==================== 点击分析完成 ====================');
  }

  /**
   * 检测当前页面状态
   */
  private detectCurrentPageState(): 'email' | 'verification' | 'password' | 'terms' | 'unknown' {
    console.log('🔍 ==================== 检测页面状态 ====================');

    // 检查条款同意页面 - 放宽检测条件
    const termsCheckbox = document.querySelector('input[type="checkbox"]');
    const termsText = document.body.textContent || '';
    const hasWelcomeText = termsText.includes('Welcome to Augment Code') || termsText.includes('Welcome');
    const hasTermsText = termsText.includes('I agree to the terms of service') ||
                        termsText.includes('terms of service') ||
                        termsText.includes('terms') ||
                        termsText.includes('agree');
    const hasButton = document.querySelector('button');
    const isTermsUrl = window.location.href.includes('terms-accept') || window.location.href.includes('terms');

    console.log('🔍 条款同意页面检测详情:');
    console.log('  - 找到复选框:', !!termsCheckbox);
    console.log('  - 找到欢迎文本:', hasWelcomeText);
    console.log('  - 找到条款文本:', hasTermsText);
    console.log('  - 找到按钮:', !!hasButton);
    console.log('  - URL包含terms:', isTermsUrl);
    console.log('  - 页面文本片段:', termsText.substring(0, 200));

    // 更宽松的条件：有复选框 + (有条款文本 或 URL包含terms) + 有按钮
    if (termsCheckbox && (hasTermsText || isTermsUrl) && hasButton) {
      console.log('✅ 检测到条款同意页面');
      return 'terms';
    }

    // 检查验证码输入页面
    const verificationInputs = document.querySelectorAll(
      'input[type="text"][placeholder*="code"], input[type="text"][placeholder*="验证"], input[name*="code"], input[id*="code"]'
    );
    if (verificationInputs.length > 0) {
      console.log('🔍 检测到验证码输入页面');
      return 'verification';
    }

    // 检查密码输入页面
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    if (passwordInputs.length > 0) {
      console.log('🔍 检测到密码输入页面');
      return 'password';
    }

    // 检查邮箱输入页面
    const emailInputs = document.querySelectorAll(
      'input[type="email"], input[name*="email"], input[placeholder*="email"], input[placeholder*="邮箱"]'
    );
    if (emailInputs.length > 0) {
      console.log('🔍 检测到邮箱输入页面');
      return 'email';
    }

    console.log('🔍 未知页面状态');
    return 'unknown';
  }

  /**
   * 更新页面状态并记录变化
   */
  private updatePageState(): void {
    // 如果已经在验证码页面，完全停止所有检测，避免干扰
    if (this.currentPageState === 'verification') {
      return;
    }

    const newState = this.detectCurrentPageState();
    if (newState !== this.currentPageState) {
      console.log(`📄 页面状态变化: ${this.currentPageState} -> ${newState}`);

      // 如果进入验证码页面，进入验证码专用模式
      if (newState === 'verification') {
        console.log('🔍 进入验证码页面，Content Script进入验证码专用模式');
        this.enterVerificationMode();
        this.currentPageState = newState;
        return;
      }

      // 如果进入条款同意页面，自动处理条款同意
      if (newState === 'terms') {
        console.log('📋 进入条款同意页面，准备自动处理');
        this.currentPageState = newState;
        // 延迟一下让页面完全加载
        setTimeout(() => {
          this.handleTermsAgreementPage();
        }, 1000);
        return;
      }

      this.currentPageState = newState;
    }
  }

  /**
   * 等待页面跳转到条款同意页面
   */
  private waitForTermsPage(): void {
    console.log('⏳ 开始等待页面跳转到条款同意页面...');

    let checkCount = 0;
    const maxChecks = 30; // 最多检查30次（30秒）

    const checkInterval = setInterval(() => {
      checkCount++;
      console.log(`⏳ 第${checkCount}次检查页面状态...`);

      // 更新页面状态
      const currentState = this.detectCurrentPageState();
      console.log('当前页面状态:', currentState);

      if (currentState === 'terms') {
        console.log('✅ 检测到条款同意页面！');
        clearInterval(checkInterval);
        this.currentPageState = 'terms';
        this.updateStatus('检测到条款页面，准备处理...', 'running');

        // 延迟一下让页面完全加载
        setTimeout(() => {
          this.handleTermsAgreementPage();
        }, 1000);
        return;
      }

      if (checkCount >= maxChecks) {
        console.log('❌ 等待条款页面超时');
        clearInterval(checkInterval);
        this.updateStatus('等待条款页面超时，请手动操作', 'error');
        this.isAutomationRunning = false;
        this.updateVerificationModeUI();
        return;
      }

      // 更新状态显示剩余等待时间
      const remainingTime = maxChecks - checkCount;
      this.updateStatus(`等待页面跳转... (${remainingTime}s)`, 'running');

    }, 1000); // 每秒检查一次
  }

  /**
   * 处理条款同意页面的自动化
   */
  private async handleTermsAgreementPage(): Promise<void> {
    try {
      console.log('📋 ==================== 开始处理条款同意页面 ====================');

      // 确保邮箱地址显示正确
      await this.ensureEmailDisplay();

      // 更新状态
      this.updateStatus('正在处理条款同意...', 'running');

      // 步骤0: 智能检查并处理Cloudflare验证（如果存在）
      console.log('🔒 步骤0: 检查是否存在Cloudflare验证...');
      await this.handleCloudflareVerificationIfExists();

      // 步骤1: 查找并勾选条款同意复选框
      console.log('📋 步骤1: 查找条款同意复选框...');
      const checkbox = this.findTermsCheckbox();

      if (!checkbox) {
        throw new Error('未找到条款同意复选框');
      }

      console.log('✅ 找到条款同意复选框:', checkbox);

      // 检查复选框是否已经勾选
      if (!checkbox.checked) {
        console.log('📋 勾选条款同意复选框...');

        // 尝试多种方法勾选复选框
        let success = false;

        // 方法1: 直接设置checked属性
        console.log('📋 方法1: 直接设置checked属性...');
        checkbox.checked = true;
        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        await this.sleep(200);

        if (checkbox.checked) {
          console.log('✅ 方法1成功：复选框已勾选');
          success = true;
        } else {
          console.log('❌ 方法1失败，尝试方法2...');

          // 方法2: 模拟点击事件
          console.log('📋 方法2: 模拟点击事件...');
          await this.simulateClick(checkbox);
          await this.sleep(300);

          if (checkbox.checked) {
            console.log('✅ 方法2成功：复选框已勾选');
            success = true;
          } else {
            console.log('❌ 方法2失败，尝试方法3...');

            // 方法3: 触发多个事件
            console.log('📋 方法3: 触发多个事件...');
            checkbox.focus();
            checkbox.click();
            checkbox.dispatchEvent(new Event('input', { bubbles: true }));
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            await this.sleep(300);

            if (checkbox.checked) {
              console.log('✅ 方法3成功：复选框已勾选');
              success = true;
            }
          }
        }

        if (success) {
          console.log('✅ 条款同意复选框已成功勾选');
          this.updateStatus('条款已同意，准备注册...', 'running');
        } else {
          console.log('❌ 所有方法都失败了，但继续执行...');
          this.updateStatus('尝试勾选条款，继续注册...', 'running');
        }
      } else {
        console.log('✅ 条款同意复选框已经勾选');
        this.updateStatus('条款已同意，准备注册...', 'running');
      }

      // 步骤2: 等待一下让用户看到勾选状态
      await this.sleep(1000);

      // 步骤3: 查找并点击注册按钮
      console.log('📋 步骤2: 查找注册按钮...');
      const signUpButton = this.findSignUpButton();

      if (!signUpButton) {
        throw new Error('未找到注册按钮');
      }

      console.log('✅ 找到注册按钮:', signUpButton);
      console.log('按钮文本:', signUpButton.textContent?.trim());

      // 检查按钮是否可点击
      if ((signUpButton as HTMLButtonElement).disabled) {
        throw new Error('注册按钮不可点击');
      }

      // 模拟人工点击注册按钮
      console.log('📋 点击注册按钮...');
      this.updateStatus('正在提交注册...', 'running');

      await this.simulateClick(signUpButton);

      console.log('✅ 注册按钮已点击');
      this.updateStatus('✅ 注册已提交！', 'success');

      // 等待页面跳转
      setTimeout(() => {
        this.updateStatus('等待注册完成...', 'running');
      }, 2000);

    } catch (error) {
      console.error('❌ 处理条款同意页面失败:', error);
      this.updateStatus(`条款处理失败: ${error instanceof Error ? error.message : String(error)}`, 'error');
    }
  }

  /**
   * 查找条款同意复选框
   */
  private findTermsCheckbox(): HTMLInputElement | null {
    console.log('🔍 查找条款同意复选框...');

    // 方法1: 查找页面上的复选框
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    console.log('找到复选框数量:', checkboxes.length);

    for (let i = 0; i < checkboxes.length; i++) {
      const checkbox = checkboxes[i] as HTMLInputElement;
      console.log(`复选框 ${i + 1}:`, {
        id: checkbox.id,
        name: checkbox.name,
        className: checkbox.className,
        checked: checkbox.checked
      });

      // 检查复选框附近的文本是否包含条款相关内容
      const parentElement = checkbox.closest('label') || checkbox.parentElement;
      if (parentElement) {
        const text = parentElement.textContent || '';
        console.log(`复选框 ${i + 1} 相关文本:`, text.substring(0, 100));

        if (text.includes('terms of service') ||
            text.includes('agree') ||
            text.includes('同意') ||
            text.includes('条款')) {
          console.log('✅ 找到条款同意复选框');
          return checkbox;
        }
      }
    }

    // 方法2: 如果只有一个复选框，很可能就是条款同意复选框
    if (checkboxes.length === 1) {
      console.log('✅ 页面只有一个复选框，假设为条款同意复选框');
      return checkboxes[0] as HTMLInputElement;
    }

    console.log('❌ 未找到条款同意复选框');
    return null;
  }

  /**
   * 查找注册按钮
   */
  private findSignUpButton(): HTMLElement | null {
    console.log('🔍 查找注册按钮...');

    // 方法1: 查找包含特定文本的按钮
    const buttons = document.querySelectorAll('button');
    console.log('找到按钮数量:', buttons.length);

    for (let i = 0; i < buttons.length; i++) {
      const button = buttons[i];
      const buttonText = button.textContent?.trim() || '';
      console.log(`按钮 ${i + 1}:`, {
        text: buttonText,
        className: button.className,
        disabled: (button as HTMLButtonElement).disabled
      });

      if (buttonText.includes('Sign up') ||
          buttonText.includes('start coding') ||
          buttonText.includes('注册') ||
          buttonText.includes('开始')) {
        console.log('✅ 找到注册按钮');
        return button;
      }
    }

    // 方法2: 查找type="submit"的按钮
    const submitButtons = document.querySelectorAll('button[type="submit"], input[type="submit"]');
    if (submitButtons.length > 0) {
      console.log('✅ 找到提交按钮，假设为注册按钮');
      return submitButtons[0] as HTMLElement;
    }

    // 方法3: 如果只有一个按钮，很可能就是注册按钮
    if (buttons.length === 1) {
      console.log('✅ 页面只有一个按钮，假设为注册按钮');
      return buttons[0];
    }

    console.log('❌ 未找到注册按钮');
    return null;
  }

  /**
   * 停止所有定时器
   */
  private stopAllTimers(): void {
    if (this.monitoringTimer) {
      console.log('🛑 清除验证码监控定时器');
      clearTimeout(this.monitoringTimer);
      this.monitoringTimer = null;
    }
  }

  /**
   * 进入静默模式 - 完全停止所有活动，避免干扰验证码页面
   */
  // @ts-ignore - 备用方法，暂时未使用
  private enterSilentMode(): void {
    console.log('🔇 ==================== 进入静默模式 ====================');

    // 停止所有定时器
    this.stopAllTimers();

    // 停止自动化
    this.isAutomationRunning = false;
    this.autoMonitorEnabled = false;

    // 隐藏状态UI，避免干扰页面
    if (this.statusUI) {
      this.statusUI.style.display = 'none';
      console.log('🔇 已隐藏状态UI');
    }

    // 更新状态为静默模式
    this.updateStatus('静默模式 - 请手动操作', 'ready');

    // 移除所有可能的事件监听器
    this.removeAllEventListeners();

    console.log('🔇 Content Script已进入静默模式，不再执行任何操作');
  }

  /**
   * 进入验证码专用模式 - 只允许验证码相关操作，避免其他干扰
   */
  private enterVerificationMode(): void {
    console.log('🔍 ==================== 进入验证码专用模式 ====================');

    // 停止可能干扰的定时器，但保留验证码监控能力
    this.stopAllTimers();

    // 使用统一的状态UI系统（与注册页面相同）
    if (!this.statusUI) {
      this.createStatusUI();
    }

    // 在验证码页面默认启用自动监控（因为用户的目标就是自动化）
    if (!this.autoMonitorEnabled) {
      console.log('🔍 检测到验证码页面，自动启用验证码监控');
      this.autoMonitorEnabled = true;
    }

    // 更新UI以反映验证码监控状态
    this.updateVerificationModeUI();

    // 启用验证码自动监控
    if (this.autoMonitorEnabled) {
      console.log('🔍 自动监控已启用，开始验证码监控');
      this.updateStatus('准备开始验证码监控...', 'running');
      this.startVerificationCodeMonitoring();
    } else {
      console.log('📝 自动监控未启用，等待手动输入');
      this.updateStatus('请手动输入验证码', 'ready');
    }

    console.log('🔍 验证码专用模式已启动');
  }

  /**
   * 更新UI以适应验证码监控模式
   */
  private updateVerificationModeUI(): void {
    if (!this.statusUI) return;

    // 更新标题
    const titleElement = this.statusUI.querySelector('.status-title');
    if (titleElement) {
      titleElement.textContent = '🔍 验证码监控助手';
    }

    // 更新按钮状态
    const startBtn = this.statusUI.querySelector('.start-btn') as HTMLButtonElement;
    const stopBtn = this.statusUI.querySelector('.stop-btn') as HTMLButtonElement;

    if (startBtn) {
      startBtn.textContent = this.isAutomationRunning ? '监控中...' : '开始监控';
      startBtn.disabled = this.isAutomationRunning;
    }

    if (stopBtn) {
      stopBtn.disabled = !this.isAutomationRunning;
    }

    // 更新邮箱信息显示
    const emailValue = this.statusUI.querySelector('.info-value');
    if (emailValue) {
      emailValue.textContent = this.currentEmail || '未设置';
    }

    console.log('✅ 验证码监控UI已更新');
  }



  /**
   * 移除所有事件监听器
   */
  private removeAllEventListeners(): void {
    try {
      // 移除可能的导航监听器（如果存在的话）
      // 由于我们没有定义这些处理器，所以注释掉
      // window.removeEventListener('beforeunload', this.handleBeforeUnload);
      // window.removeEventListener('unload', this.handleUnload);

      console.log('🔇 已移除所有事件监听器');
    } catch (error) {
      console.log('🔇 移除事件监听器时出错（可能本来就没有）:', error);
    }
  }

  /**
   * 监控页面状态变化，检测是否成功前进
   */
  // @ts-ignore - 备用方法，暂时未使用
  private async monitorPageProgress(expectedChanges: string[] = []): Promise<boolean> {
    console.log('👁️ ==================== 开始页面进度监控 ====================');

    const initialState = {
      url: window.location.href,
      title: document.title,
      pathname: window.location.pathname
    };

    console.log('👁️ 初始状态:', initialState);

    // 监控5秒钟
    for (let i = 0; i < 10; i++) {
      await this.sleep(500);

      const currentState = {
        url: window.location.href,
        title: document.title,
        pathname: window.location.pathname
      };

      // 检查是否有变化
      const hasUrlChange = initialState.url !== currentState.url;
      const hasTitleChange = initialState.title !== currentState.title;
      const hasPathChange = initialState.pathname !== currentState.pathname;

      if (hasUrlChange || hasTitleChange || hasPathChange) {
        console.log('✅ 检测到页面进度变化:');
        console.log('✅ URL变化:', hasUrlChange, currentState.url);
        console.log('✅ 标题变化:', hasTitleChange, currentState.title);
        console.log('✅ 路径变化:', hasPathChange, currentState.pathname);
        return true;
      }

      // 检查页面内容是否有特定的变化指示器
      for (const indicator of expectedChanges) {
        if (document.body.innerHTML.includes(indicator)) {
          console.log('✅ 检测到预期的页面内容变化:', indicator);
          return true;
        }
      }

      console.log(`👁️ 监控中... (${i + 1}/10)`);
    }

    console.log('❌ 未检测到页面进度变化');
    return false;
  }

  /**
   * 检查页面变化
   */
  // @ts-ignore - 备用方法，暂时未使用
  private checkForPageChanges(): boolean {
    console.log('🔍 检查页面变化...');
    console.log('当前URL:', window.location.href);

    // 检查是否出现了密码输入框（注册下一步）
    const passwordInput = document.querySelector('input[type="password"]');
    if (passwordInput) {
      console.log('✅ 发现密码输入框，页面已进入下一步');
      return true;
    }

    // 检查是否出现了验证码输入框
    const codeInput = this.findVerificationCodeInput();
    if (codeInput) {
      console.log('✅ 发现验证码输入框，页面已进入下一步');
      return true;
    }

    // 检查是否出现了错误信息
    const errorElements = document.querySelectorAll('[class*="error"], [class*="alert"], [role="alert"]');
    if (errorElements.length > 0) {
      console.log('⚠️ 发现错误信息:');
      errorElements.forEach((el, index) => {
        console.log(`  ${index + 1}. ${el.textContent?.trim()}`);
      });
    }

    // 检查页面是否有新的表单元素
    const forms = document.querySelectorAll('form');
    console.log(`📋 页面上有 ${forms.length} 个表单`);

    return false;
  }

  /**
   * 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }



  /**
   * 获取页面状态
   */
  public getPageStatus() {
    return {
      isReady: this.isReady,
      currentEmail: this.currentEmail,
      autoMonitorEnabled: this.autoMonitorEnabled,
      isRegistrationPage: this.isRegistrationPage(),
      hasEmailInput: !!this.findEmailInput(),
      hasSendCodeButton: !!this.findSendCodeButton(),
      hasCodeInput: !!this.findVerificationCodeInput(),
      hasSubmitButton: !!this.findSubmitButton()
    };
  }

  /**
   * 等待Cloudflare验证Success状态
   */
  private async waitForCloudflareSuccess(maxWaitTime: number = 15000): Promise<boolean> {
    console.log('🔒 ==================== 等待Cloudflare验证Success ====================');
    console.log(`⏱️ 最大等待时间: ${maxWaitTime}ms`);

    const startTime = Date.now();
    let lastInteractionTime = 0;

    while (Date.now() - startTime < maxWaitTime) {
      // 查找Cloudflare验证元素
      const cfElement = this.findCloudflareVerificationCheckbox();

      if (!cfElement) {
        console.log('✅ 未找到CF验证元素，验证可能已完成或不存在');
        return true;
      }

      console.log('🔍 找到CF验证元素，检查状态...');

      // 检查是否显示Success状态
      if (this.isCloudflareShowingSuccess(cfElement)) {
        console.log('✅ 检测到Cloudflare Success状态');
        return true;
      }

      // 如果发现需要点击的验证框，且距离上次交互超过3秒，尝试模拟鼠标点击
      const currentTime = Date.now();
      if (this.needsCloudflareInteraction(cfElement) && (currentTime - lastInteractionTime > 3000)) {
        console.log('🖱️ 检测到需要人机验证交互，模拟鼠标点击...');
        await this.simulateHumanMouseClick(cfElement);
        lastInteractionTime = currentTime;

        // 点击后等待一段时间让验证处理
        await this.sleep(2000);
      } else {
        // 等待1秒后再次检查
        await this.sleep(1000);
      }
    }

    console.log('⏰ 等待Cloudflare验证Success超时');
    return false;
  }

  /**
   * 智能检查并处理Cloudflare验证（如果存在）
   */
  private async handleCloudflareVerificationIfExists(): Promise<void> {
    try {
      console.log('🔒 ==================== 智能检查Cloudflare验证 ====================');

      // 直接等待CF验证完成，不进行复杂的绕过操作
      const cfVerified = await this.waitForCloudflareSuccess(15000);

      if (cfVerified) {
        console.log('✅ Cloudflare验证已完成');
        this.updateStatus('人机验证已完成', 'success');
      } else {
        console.log('⚠️ Cloudflare验证未完成，但继续执行');
        this.updateStatus('人机验证超时，继续执行...', 'running');
      }

    } catch (error) {
      console.error('❌ 处理Cloudflare验证时出错:', error);
      this.updateStatus('人机验证处理出错，继续执行...', 'running');
    }
  }

  /**
   * 检查Cloudflare是否显示Success状态
   */
  private isCloudflareShowingSuccess(element: HTMLElement): boolean {
    // 检查元素及其子元素是否包含Success相关的文本或类名
    const textContent = element.textContent?.toLowerCase() || '';
    const innerHTML = element.innerHTML.toLowerCase();

    // 检查Success文本
    const successTexts = ['success', '成功', 'verified', '已验证', 'complete', '完成'];
    for (const text of successTexts) {
      if (textContent.includes(text) || innerHTML.includes(text)) {
        console.log(`✅ 发现Success文本: ${text}`);
        return true;
      }
    }

    // 检查Success相关的CSS类名
    const successClasses = ['success', 'verified', 'complete', 'passed'];
    for (const className of successClasses) {
      if (element.classList.contains(className) ||
          element.querySelector(`.${className}`) ||
          innerHTML.includes(`class="${className}"`) ||
          innerHTML.includes(`class='${className}'`)) {
        console.log(`✅ 发现Success类名: ${className}`);
        return true;
      }
    }

    // 检查是否有绿色勾选标记
    const checkmarks = element.querySelectorAll('svg, .checkmark, [class*="check"], [class*="tick"]');
    for (let i = 0; i < checkmarks.length; i++) {
      const checkmark = checkmarks[i];
      const style = window.getComputedStyle(checkmark);
      if (style.color.includes('green') || style.fill.includes('green') ||
          checkmark.classList.toString().includes('success')) {
        console.log('✅ 发现绿色勾选标记');
        return true;
      }
    }

    return false;
  }

  /**
   * 检查是否需要Cloudflare交互
   */
  private needsCloudflareInteraction(element: HTMLElement): boolean {
    // 检查是否有未勾选的复选框
    const checkboxes = element.querySelectorAll('input[type="checkbox"], [role="checkbox"]');
    for (let i = 0; i < checkboxes.length; i++) {
      const checkbox = checkboxes[i];
      const input = checkbox as HTMLInputElement;
      if (!input.checked && input.getAttribute('aria-checked') !== 'true') {
        console.log('🔍 发现未勾选的验证复选框');
        return true;
      }
    }

    // 检查是否有"点击验证"相关的文本
    const textContent = element.textContent?.toLowerCase() || '';
    const interactionTexts = ['verify', 'click', 'check', '验证', '点击', '勾选'];
    for (const text of interactionTexts) {
      if (textContent.includes(text)) {
        console.log(`🔍 发现交互提示文本: ${text}`);
        return true;
      }
    }

    return false;
  }

  /**
   * 模拟真实的人类鼠标点击
   */
  private async simulateHumanMouseClick(element: HTMLElement): Promise<void> {
    try {
      console.log('🖱️ 开始模拟真实人类鼠标点击...');

      // 确保元素可见
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await this.sleep(500);

      // 获取元素的位置和大小
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      // 添加一些随机偏移，模拟人类不完美的点击
      const offsetX = (Math.random() - 0.5) * Math.min(rect.width * 0.3, 10);
      const offsetY = (Math.random() - 0.5) * Math.min(rect.height * 0.3, 10);

      const clickX = centerX + offsetX;
      const clickY = centerY + offsetY;

      console.log(`🎯 点击坐标: (${clickX.toFixed(1)}, ${clickY.toFixed(1)})`);

      // 模拟鼠标移动到目标位置
      const mouseMoveEvent = new MouseEvent('mousemove', {
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY,
        button: 0
      });
      element.dispatchEvent(mouseMoveEvent);

      // 短暂延迟，模拟人类反应时间
      await this.sleep(100 + Math.random() * 200);

      // 鼠标按下
      const mouseDownEvent = new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY,
        button: 0
      });
      element.dispatchEvent(mouseDownEvent);

      // 短暂延迟，模拟按键持续时间
      await this.sleep(50 + Math.random() * 100);

      // 鼠标抬起
      const mouseUpEvent = new MouseEvent('mouseup', {
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY,
        button: 0
      });
      element.dispatchEvent(mouseUpEvent);

      // 点击事件
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY,
        button: 0
      });
      element.dispatchEvent(clickEvent);

      // 如果是复选框，也尝试直接点击
      const checkbox = element.querySelector('input[type="checkbox"]') as HTMLInputElement;
      if (checkbox) {
        console.log('🎯 直接点击复选框元素');
        checkbox.click();
      }

      console.log('✅ 人类鼠标点击模拟完成');

    } catch (error) {
      console.error('❌ 模拟鼠标点击失败:', error);
      // 备用方案：直接点击
      element.click();
    }
  }

  /**
   * 查找Cloudflare验证复选框
   */
  private findCloudflareVerificationCheckbox(): HTMLElement | null {
    console.log('🔍 查找Cloudflare验证复选框...');

    // 方法1: 查找Turnstile iframe的父容器中的可点击元素
    const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
    if (turnstileIframes.length > 0) {
      console.log('✅ 找到Cloudflare Turnstile iframe:', turnstileIframes.length);

      // 查找iframe的父容器
      const iframe = turnstileIframes[0];
      let parent = iframe.parentElement;

      // 向上查找3层父元素，寻找可点击的复选框区域
      for (let i = 0; i < 3 && parent; i++) {
        // 查找父容器中的复选框或可点击区域
        const clickableArea = parent.querySelector('input[type="checkbox"]') ||
                             parent.querySelector('[role="checkbox"]') ||
                             parent.querySelector('.cf-turnstile') ||
                             parent;

        if (clickableArea && clickableArea !== iframe) {
          console.log('✅ 找到iframe外部的可点击验证区域');
          return clickableArea as HTMLElement;
        }

        parent = parent.parentElement;
      }

      // 如果找不到外部可点击区域，返回iframe容器
      console.log('⚠️ 未找到外部可点击区域，返回iframe容器');
      return iframe.parentElement as HTMLElement || iframe as HTMLElement;
    }

    // 方法2: 查找包含"Verify you are human"文本的元素
    const verifyTexts = ['Verify you are human', '验证您是人类', 'I am human', 'Human verification'];

    for (const verifyText of verifyTexts) {
      const textElements = Array.from(document.querySelectorAll('*')).filter(el =>
        el.textContent?.includes(verifyText)
      );

      for (const element of textElements) {
        console.log('✅ 找到包含验证文本的元素:', verifyText);

        // 查找附近的复选框或可点击元素
        const searchElements = [
          element,
          element.parentElement,
          element.parentElement?.parentElement,
          element.closest('form'),
          element.closest('div')
        ].filter(Boolean);

        for (const searchElement of searchElements) {
          if (!searchElement) continue;

          // 查找复选框
          const checkbox = searchElement.querySelector('input[type="checkbox"]') ||
                          searchElement.querySelector('[role="checkbox"]') ||
                          searchElement.querySelector('.cf-turnstile') ||
                          searchElement.querySelector('[data-sitekey]');

          if (checkbox) {
            console.log('✅ 找到验证文本附近的可点击元素');
            return checkbox as HTMLElement;
          }
        }

        // 如果找不到具体的复选框，返回包含文本的元素本身
        console.log('⚠️ 未找到具体复选框，返回文本元素本身');
        return element as HTMLElement;
      }
    }

    // 方法3: 查找Cloudflare相关的CSS类名
    const cloudflareSelectors = [
      '.cf-turnstile',
      '.cf-challenge',
      '[data-sitekey]',
      '.cloudflare-turnstile'
    ];

    for (const selector of cloudflareSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        console.log(`✅ 通过选择器找到Cloudflare元素: ${selector}`);
        return element as HTMLElement;
      }
    }

    console.log('❌ 未找到Cloudflare验证复选框');
    return null;
  }



  /**
   * 处理注册完成
   */
  private async handleRegistrationComplete(): Promise<void> {
    try {
      console.log('🎉 ========== 注册完成处理开始 ==========');
      this.updateStatus('🎉 注册成功！正在处理...', 'success');

      // 获取当前用户信息
      const userInfo = this.extractUserInfo();
      console.log('👤 用户信息:', userInfo);

      // 记录注册成功
      await this.recordRegistrationSuccess(userInfo);

      // 等待2秒让用户看到成功信息
      await this.sleep(2000);

      // 选择免费计划
      await this.selectFreePlan();

      // 🎯 在退出账号前先清理浏览器痕迹
      console.log('🧹 开始预清理浏览器痕迹...');
      await this.clearBrowserTraces();

      // 🎯 在退出账号前先切换代理服务器
      console.log('🔄 开始切换到新代理...');
      await this.switchProxy();

      // 等待一下让代理和清理生效
      console.log('⏳ 等待5秒让代理和清理设置生效...');
      await this.sleep(5000);

      // 查找并点击Logout按钮
      await this.performLogout();

      // 等待退出完成后跳转回首页
      await this.waitForLogoutComplete();

      console.log('✅ ========== 注册完成处理结束 ==========');

      // 停止全局超时监控
      this.stopGlobalTimeout();

    } catch (error) {
      console.error('❌ 处理注册完成失败:', error);
      this.updateStatus(`注册完成处理失败: ${error instanceof Error ? error.message : String(error)}`, 'error');
      // 出错时也停止全局超时监控
      this.stopGlobalTimeout();
    }
  }

  /**
   * 提取用户信息
   */
  private extractUserInfo(): any {
    const url = window.location.href;
    const userInfo = {
      email: '',
      registrationTime: new Date().toISOString(),
      successUrl: url,
      userAgent: navigator.userAgent
    };

    // 尝试从页面中提取邮箱信息
    const emailSelectors = [
      '.user-email',
      '.account-email',
      '[class*="email"]',
      '.user-info',
      '.account-info'
    ];

    for (const selector of emailSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const text = element.textContent || '';
        const emailMatch = text.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        if (emailMatch) {
          userInfo.email = emailMatch[0];
          console.log('📧 从页面提取到邮箱:', userInfo.email);
          break;
        }
      }
    }

    // 如果页面没有邮箱信息，尝试从URL或其他地方获取
    if (!userInfo.email) {
      // 可以从localStorage或其他地方获取
      const storedEmail = localStorage.getItem('currentEmail') || sessionStorage.getItem('currentEmail');
      if (storedEmail) {
        userInfo.email = storedEmail;
        console.log('📧 从存储中获取邮箱:', userInfo.email);
      }
    }

    return userInfo;
  }

  /**
   * 记录注册成功
   */
  private async recordRegistrationSuccess(userInfo: any): Promise<void> {
    try {
      console.log('📝 记录注册成功信息...');

      // 发送消息给background script记录成功
      await chrome.runtime.sendMessage({
        type: 'REGISTRATION_SUCCESS',
        data: userInfo
      });

      console.log('✅ 注册成功信息已记录');
      this.updateStatus('✅ 注册成功已记录', 'success');

    } catch (error) {
      console.error('❌ 记录注册成功失败:', error);
    }
  }

  /**
   * 执行退出登录
   */
  private async performLogout(): Promise<void> {
    console.log('🚪 开始执行退出登录...');
    this.updateStatus('正在退出登录...', 'running');

    // 查找Logout按钮
    const allButtons = Array.from(document.querySelectorAll('button, a, [role="button"]'));
    let logoutButton: HTMLElement | null = null;

    for (const button of allButtons) {
      const text = button.textContent?.toLowerCase().trim() || '';
      const href = (button as HTMLAnchorElement).href?.toLowerCase() || '';

      if (text.includes('logout') || text.includes('sign out') || text.includes('log out') ||
          text.includes('退出') || text.includes('登出') ||
          href.includes('logout')) {
        logoutButton = button as HTMLElement;
        console.log('🎯 找到Logout按钮:', text || href);
        break;
      }
    }

    if (logoutButton) {
      console.log('🖱️ 点击Logout按钮...');
      logoutButton.click();
      this.updateStatus('✅ 已点击退出按钮', 'success');
      await this.sleep(1000);
    } else {
      console.log('⚠️ 未找到Logout按钮');
      this.updateStatus('⚠️ 未找到退出按钮', 'error');
    }
  }

  /**
   * 等待退出完成
   */
  private async waitForLogoutComplete(): Promise<void> {
    console.log('⏳ 等待退出完成...');
    this.updateStatus('等待退出完成...', 'running');

    const maxWaitTime = 10000; // 10秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const currentUrl = window.location.href.toLowerCase();

      // 检查是否已经跳转到首页或登录页
      if (currentUrl.includes('app.augmentcode.com/') &&
          !currentUrl.includes('/account/') &&
          !currentUrl.includes('/dashboard')) {
        console.log('✅ 退出完成，已跳转到首页');
        this.updateStatus('✅ 退出完成！正在进行最终清理...', 'running');

        // 🎯 退出完成后进行最终清理，确保彻底清除痕迹
        console.log('🧹 开始最终清理，确保彻底清除所有痕迹...');
        await this.performFinalCleanup();

        // 等待2秒后准备下一次注册
        console.log('⏳ 等待2秒后准备下一次注册...');
        await this.sleep(2000);

        this.updateStatus('✅ 清理完成！正在无痕模式下开始下一次注册...', 'success');

        // 🔒 在无痕模式下打开新窗口进行下一次注册
        console.log('🔒 在无痕模式下打开新窗口...');
        await this.openNextRegistrationInIncognito();

        return;
      }

      await this.sleep(500);
    }

    console.log('⏰ 等待退出完成超时');
    this.updateStatus('⏰ 退出等待超时，继续下一步', 'error');
  }

  /**
   * 显示注册记录模态窗口
   */
  private async showRegistrationRecords(): Promise<void> {
    try {
      console.log('📋 显示注册记录...');

      // 从Chrome存储获取注册记录
      const result = await chrome.runtime.sendMessage({
        type: 'GET_REGISTRATION_RECORDS'
      });

      if (!result.success) {
        this.updateStatus('获取注册记录失败', 'error');
        return;
      }

      const records = result.data || [];
      this.createRecordsModal(records);

    } catch (error) {
      console.error('❌ 显示注册记录失败:', error);
      this.updateStatus('显示注册记录失败', 'error');
    }
  }

  /**
   * 创建注册记录模态窗口
   */
  private createRecordsModal(records: any[]): void {
    // 移除现有的模态窗口
    const existingModal = document.getElementById('registration-records-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // 创建模态窗口
    const modal = document.createElement('div');
    modal.id = 'registration-records-modal';
    modal.innerHTML = `
      <div class="modal-overlay">
        <div class="modal-content">
          <div class="modal-header">
            <h3>📋 注册记录 (${records.length} 条)</h3>
            <button class="modal-close">×</button>
          </div>
          <div class="modal-body">
            ${records.length === 0 ?
              '<div class="no-records">暂无注册记录</div>' :
              records.map((record, index) => `
                <div class="record-item">
                  <div class="record-header">
                    <span class="record-index">#${index + 1}</span>
                    <span class="record-email">${record.email || '未知邮箱'}</span>
                    <span class="record-time">${new Date(record.registrationTime || record.timestamp).toLocaleString()}</span>
                  </div>
                  <div class="record-details">
                    <div class="detail-item">
                      <span class="detail-label">成功URL:</span>
                      <span class="detail-value">${record.successUrl || '未知'}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">用户代理:</span>
                      <span class="detail-value">${(record.userAgent || '未知').substring(0, 50)}...</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">记录ID:</span>
                      <span class="detail-value">${record.id || '未知'}</span>
                    </div>
                  </div>
                </div>
              `).join('')
            }
          </div>
          <div class="modal-footer">
            <button class="modal-btn export-all-btn">📤 导出全部</button>
            <button class="modal-btn close-btn">关闭</button>
          </div>
        </div>
      </div>
    `;

    // 添加模态窗口样式
    this.addModalStyles();

    // 添加事件监听器
    const closeBtn = modal.querySelector('.modal-close');
    const closeBtnFooter = modal.querySelector('.close-btn');
    const exportAllBtn = modal.querySelector('.export-all-btn');

    const closeModal = () => modal.remove();

    closeBtn?.addEventListener('click', closeModal);
    closeBtnFooter?.addEventListener('click', closeModal);
    exportAllBtn?.addEventListener('click', async () => {
      await this.exportRegistrationRecords();
      closeModal();
    });

    // 点击遮罩层关闭
    modal.querySelector('.modal-overlay')?.addEventListener('click', (e) => {
      if (e.target === modal.querySelector('.modal-overlay')) {
        closeModal();
      }
    });

    document.body.appendChild(modal);
  }

  /**
   * 添加模态窗口样式
   */
  private addModalStyles(): void {
    const existingStyle = document.getElementById('modal-styles');
    if (existingStyle) return;

    const style = document.createElement('style');
    style.id = 'modal-styles';
    style.textContent = `
      #registration-records-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 20000;
      }

      .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
      }

      .modal-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 800px;
        max-height: 80vh;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid #e5e7eb;
      }

      .modal-header h3 {
        margin: 0;
        color: #1f2937;
        font-size: 18px;
        font-weight: 600;
      }

      .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #6b7280;
        padding: 0;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
      }

      .modal-close:hover {
        background: #f3f4f6;
        color: #374151;
      }

      .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 20px 24px;
      }

      .no-records {
        text-align: center;
        color: #6b7280;
        font-size: 16px;
        padding: 40px 20px;
      }

      .record-item {
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
      }

      .record-header {
        background: #f9fafb;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        border-bottom: 1px solid #e5e7eb;
      }

      .record-index {
        background: #3b82f6;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
      }

      .record-email {
        font-weight: 600;
        color: #1f2937;
        flex: 1;
      }

      .record-time {
        color: #6b7280;
        font-size: 12px;
      }

      .record-details {
        padding: 16px;
      }

      .detail-item {
        display: flex;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .detail-label {
        font-weight: 500;
        color: #374151;
        width: 100px;
        flex-shrink: 0;
      }

      .detail-value {
        color: #6b7280;
        word-break: break-all;
        flex: 1;
      }

      .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }

      .modal-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }

      .export-all-btn {
        background: #8b5cf6;
        color: white;
      }

      .export-all-btn:hover {
        background: #7c3aed;
      }

      .close-btn {
        background: #6b7280;
        color: white;
      }

      .close-btn:hover {
        background: #4b5563;
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 导出注册记录为TXT文件
   */
  private async exportRegistrationRecords(): Promise<void> {
    try {
      console.log('📤 导出注册记录...');
      this.updateStatus('正在导出注册记录...', 'running');

      // 从Chrome存储获取注册记录
      const result = await chrome.runtime.sendMessage({
        type: 'GET_REGISTRATION_RECORDS'
      });

      if (!result.success) {
        this.updateStatus('获取注册记录失败', 'error');
        return;
      }

      const records = result.data || [];

      if (records.length === 0) {
        this.updateStatus('暂无注册记录可导出', 'error');
        return;
      }

      // 生成TXT内容
      const txtContent = this.generateTxtContent(records);

      // 创建并下载文件
      const now = new Date();
      const timestamp = now.toISOString().replace(/[:.]/g, '-').substring(0, 19);
      const filename = `registration_records_${timestamp}.txt`;

      const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('✅ 注册记录导出成功:', filename);
      this.updateStatus(`✅ 已导出 ${records.length} 条记录`, 'success');

    } catch (error) {
      console.error('❌ 导出注册记录失败:', error);
      this.updateStatus('导出注册记录失败', 'error');
    }
  }

  /**
   * 生成TXT文件内容
   */
  private generateTxtContent(records: any[]): string {
    const header = `
===========================================
        Augment Code 注册记录导出
===========================================
导出时间: ${new Date().toLocaleString()}
记录总数: ${records.length}
===========================================

`;

    const recordsContent = records.map((record, index) => {
      return `
记录 #${index + 1}
----------------------------------------
邮箱地址: ${record.email || '未知'}
注册时间: ${new Date(record.registrationTime || record.timestamp).toLocaleString()}
成功URL: ${record.successUrl || '未知'}
记录ID: ${record.id || '未知'}
用户代理: ${record.userAgent || '未知'}
记录时间: ${new Date(record.timestamp).toLocaleString()}
----------------------------------------
`;
    }).join('\n');

    const footer = `
===========================================
导出完成 - 共 ${records.length} 条记录
===========================================
`;

    return header + recordsContent + footer;
  }

  /**
   * 清理浏览器痕迹
   */
  private async clearBrowserTraces(): Promise<void> {
    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🧹 开始清理浏览器痕迹... (尝试 ${attempt}/${maxRetries})`);
        this.updateStatus(`正在清理浏览器痕迹... (${attempt}/${maxRetries})`, 'running');

        // 发送消息给background script执行清理
        const result = await chrome.runtime.sendMessage({
          type: 'CLEAR_BROWSER_TRACES',
          data: {
            domain: 'augmentcode.com'
          }
        });

        if (result.success) {
          console.log('✅ 浏览器痕迹清理完成:', result.message);
          this.updateStatus('✅ 浏览器痕迹已清理', 'success');

          // 等待一下确保清理生效
          await this.sleep(2000);
          return;
        } else {
          throw new Error(result.error || '清理失败');
        }

      } catch (error) {
        lastError = error;
        console.error(`❌ 清理浏览器痕迹失败 (尝试 ${attempt}/${maxRetries}):`, error);

        if (attempt < maxRetries) {
          console.log(`⏳ 等待 ${attempt * 2} 秒后重试...`);
          await this.sleep(attempt * 2000);
        }
      }
    }

    // 所有尝试都失败了
    console.error('❌ 浏览器痕迹清理彻底失败');
    const errorMessage = lastError instanceof Error ? lastError.message : String(lastError) || '未知错误';
    this.updateStatus(`⚠️ 浏览器痕迹清理失败: ${errorMessage}`, 'error');
  }

  /**
   * 检测并显示无痕模式状态
   */
  private checkAndDisplayIncognitoStatus(): void {
    try {
      // 检测是否在无痕模式下
      const isIncognito = chrome.extension?.inIncognitoContext || false;

      if (isIncognito) {
        console.log('✅ 当前在无痕模式下运行');
        this.showIncognitoStatus('✅ 无痕模式已启用', 'success');
      } else {
        console.log('⚠️ 当前不在无痕模式下运行');
        this.showIncognitoStatus('💡 建议使用无痕模式以获得更好的隐私保护', 'warning');
      }
    } catch (error) {
      console.warn('⚠️ 无法检测无痕模式状态:', error);
    }
  }

  /**
   * 显示无痕模式状态提示
   */
  private showIncognitoStatus(message: string, type: 'success' | 'warning'): void {
    const statusElement = document.createElement('div');
    statusElement.style.cssText = `
      position: fixed;
      top: 60px;
      right: 20px;
      background: ${type === 'success' ? '#4CAF50' : '#FF9800'};
      color: white;
      padding: 10px 15px;
      border-radius: 6px;
      z-index: 10001;
      font-family: Arial, sans-serif;
      font-size: 14px;
      max-width: 300px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;

    statusElement.textContent = message;
    document.body.appendChild(statusElement);

    // 5秒后自动消失
    setTimeout(() => {
      if (statusElement.parentNode) {
        statusElement.remove();
      }
    }, 5000);
  }

  /**
   * 在无痕模式下打开下一次注册
   */
  private async openNextRegistrationInIncognito(): Promise<void> {
    try {
      console.log('🔒 ========== 开始在无痕模式下打开新窗口 ==========');

      // 发送消息给background script创建无痕窗口
      const response = await chrome.runtime.sendMessage({
        type: 'OPEN_IN_INCOGNITO',
        data: {
          url: 'https://app.augmentcode.com/'
        }
      });

      if (response.success) {
        console.log('✅ 无痕窗口创建成功，窗口ID:', response.windowId);
        this.updateStatus('✅ 无痕窗口已创建！', 'success');

        // 等待一下让无痕窗口完全加载
        await this.sleep(2000);

        // 发送消息通知可以开始下一次注册
        await chrome.runtime.sendMessage({
          type: 'READY_FOR_NEXT_REGISTRATION'
        });

        // 可选：关闭当前标签页（让用户决定）
        console.log('💡 提示：您可以手动关闭当前标签页，新的注册将在无痕窗口中进行');

      } else {
        console.error('❌ 创建无痕窗口失败:', response.error);
        this.updateStatus('❌ 创建无痕窗口失败，将在当前窗口继续', 'error');

        // 如果无痕窗口创建失败，回退到原来的逻辑
        await chrome.runtime.sendMessage({
          type: 'READY_FOR_NEXT_REGISTRATION'
        });
      }

    } catch (error) {
      console.error('❌ 无痕模式操作失败:', error);
      this.updateStatus('❌ 无痕模式操作失败，将在当前窗口继续', 'error');

      // 如果出错，回退到原来的逻辑
      await chrome.runtime.sendMessage({
        type: 'READY_FOR_NEXT_REGISTRATION'
      });
    }
  }

  /**
   * 执行最终清理
   */
  private async performFinalCleanup(): Promise<void> {
    try {
      console.log('🧹 ========== 开始最终清理 ==========');

      // 再次清理浏览器痕迹，确保彻底
      console.log('🔄 执行最终浏览器数据清理...');
      await this.clearBrowserTraces();

      // 清理页面本地存储
      console.log('🗑️ 清理页面本地存储...');
      try {
        localStorage.clear();
        sessionStorage.clear();
        console.log('✅ 页面存储清理完成');
      } catch (error) {
        console.warn('⚠️ 页面存储清理失败:', error);
      }

      // 清理页面cookies（通过document.cookie）
      console.log('🍪 清理页面cookies...');
      try {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          const eqPos = cookie.indexOf('=');
          const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
          if (name) {
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.augmentcode.com`;
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=augmentcode.com`;
            document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          }
        }
        console.log('✅ 页面cookies清理完成');
      } catch (error) {
        console.warn('⚠️ 页面cookies清理失败:', error);
      }

      console.log('✅ ========== 最终清理完成 ==========');

    } catch (error) {
      console.error('❌ 最终清理失败:', error);
    }
  }

  /**
   * 切换代理服务器
   */
  private async switchProxy(): Promise<void> {
    try {
      console.log('🔄 开始切换代理服务器...');
      this.updateStatus('正在获取新代理服务器...', 'running');

      // 发送消息给background script获取新代理
      const result = await chrome.runtime.sendMessage({
        type: 'SWITCH_PROXY'
      });

      if (result.success) {
        const { host, port, type, protocol, username, attempt, connectionTest } = result.data;
        console.log('✅ 代理服务器切换完成:', `${host}:${port} (${protocol?.toUpperCase() || type}, 用户: ${username}, 尝试 ${attempt})`);

        // 显示连接测试结果
        if (connectionTest) {
          if (connectionTest.success) {
            if (connectionTest.skipTest) {
              console.log('ℹ️ SOCKS5认证代理配置完成，已跳过连接测试');
              this.updateStatus(`✅ SOCKS5代理已切换: ${host}:${port}`, 'success');
            } else {
              console.log('🌐 代理连接测试成功, IP:', connectionTest.ip);
              this.updateStatus(`✅ SOCKS5代理已切换并验证: ${host}:${port}`, 'success');
            }
          } else {
            console.warn('⚠️ 代理连接测试失败:', connectionTest.error);
            if (connectionTest.timeout) {
              this.updateStatus(`⚠️ SOCKS5代理连接超时: ${host}:${port}`, 'success');
            } else {
              this.updateStatus(`⚠️ SOCKS5代理已设置但连接异常: ${host}:${port}`, 'success');
            }
          }
        } else {
          this.updateStatus(`✅ SOCKS5认证代理已切换: ${host}:${port}`, 'success');
        }

        // 等待代理生效
        console.log('⏳ 等待3秒让代理设置生效...');
        await this.sleep(3000);

      } else {
        console.log('⚠️ 代理服务器切换失败:', result.error);

        if (result.fallbackToNoProxy) {
          this.updateStatus('⚠️ 代理切换失败，已回退到无代理模式', 'error');
        } else {
          this.updateStatus('⚠️ 代理服务器切换失败', 'error');
        }
      }

    } catch (error) {
      console.error('❌ 切换代理服务器失败:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.updateStatus(`切换代理服务器失败: ${errorMessage}`, 'error');
    }
  }



  /**
   * 选择免费计划
   */
  private async selectFreePlan(): Promise<void> {
    try {
      console.log('💰 开始选择免费计划...');
      this.updateStatus('正在选择免费计划...', 'running');

      // 等待页面完全加载
      await this.sleep(2000);

      // 查找Change plan按钮
      const changePlanButton = await this.findChangePlanButton();
      if (!changePlanButton) {
        console.log('⚠️ 未找到Change plan按钮，可能已经是免费计划');
        this.updateStatus('未找到Change plan按钮', 'error');
        return;
      }

      console.log('🎯 找到Change plan按钮，准备点击');
      changePlanButton.click();

      // 等待弹出框出现
      await this.sleep(2000);

      // 查找并选择第一个Free免费计划
      const freePlanSelected = await this.selectFreePlanInModal();
      if (!freePlanSelected) {
        console.log('❌ 选择免费计划失败');
        this.updateStatus('选择免费计划失败', 'error');
        return;
      }

      // 等待页面返回到订阅页面
      await this.waitForSubscriptionPage();

      console.log('✅ 免费计划选择完成');
      this.updateStatus('✅ 免费计划已选择', 'success');

    } catch (error) {
      console.error('❌ 选择免费计划失败:', error);
      this.updateStatus('选择免费计划失败', 'error');
    }
  }

  /**
   * 查找Change plan按钮
   */
  private async findChangePlanButton(): Promise<HTMLElement | null> {
    const maxAttempts = 10;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`🔍 查找Change plan按钮 (${attempt}/${maxAttempts})`);

      // 查找所有可能的按钮
      const allButtons = Array.from(document.querySelectorAll('button, a, [role="button"]'));

      for (const button of allButtons) {
        const text = button.textContent?.toLowerCase().trim() || '';
        const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';

        if (text.includes('change plan') || text.includes('change') ||
            ariaLabel.includes('change plan') || ariaLabel.includes('change')) {
          console.log('🎯 找到Change plan按钮:', text || ariaLabel);
          return button as HTMLElement;
        }
      }

      // 也尝试查找包含"plan"的链接或按钮
      const planElements = Array.from(document.querySelectorAll('[class*="plan"], [id*="plan"]'));
      for (const element of planElements) {
        if (element.textContent?.toLowerCase().includes('change')) {
          console.log('🎯 找到包含change的plan元素:', element.textContent);
          return element as HTMLElement;
        }
      }

      await this.sleep(1000);
    }

    return null;
  }

  /**
   * 在弹出框中选择免费计划
   */
  private async selectFreePlanInModal(): Promise<boolean> {
    try {
      console.log('🎯 在弹出框中选择免费计划...');
      this.updateStatus('正在选择免费计划...', 'running');

      // 等待弹出框完全加载
      console.log('⏳ 等待弹出框完全加载...');
      await this.sleep(3000);

      // 打印当前页面信息用于调试
      console.log('📄 当前页面URL:', window.location.href);
      console.log('📄 页面标题:', document.title);

      // 查找模态框
      const modals = document.querySelectorAll('[role="dialog"], .modal, [class*="modal"], [class*="popup"]');
      console.log('🔍 找到模态框数量:', modals.length);

      if (modals.length > 0) {
        const modal = modals[0];
        console.log('📋 模态框内容预览:', modal.textContent?.substring(0, 200));
      }

      // 查找第一个Free计划
      console.log('🔍 开始查找Free计划元素...');
      const freePlanElement = await this.findFreePlanElement();
      if (!freePlanElement) {
        console.log('❌ 未找到Free计划元素');
        this.updateStatus('未找到免费计划选项', 'error');
        return false;
      }

      console.log('✅ 找到Free计划元素:', freePlanElement.textContent?.substring(0, 100));

      // 点击选择免费计划
      console.log('🔍 开始点击选择免费计划...');
      const planSelected = await this.clickFreePlanElement(freePlanElement);
      if (!planSelected) {
        console.log('❌ 免费计划点击失败');
        this.updateStatus('免费计划点击失败', 'error');
        return false;
      }

      console.log('✅ 免费计划点击成功');

      // 等待计划选择生效，并查找勾选同意的复选框
      console.log('⏳ 等待计划选择生效，查找同意复选框...');
      await this.sleep(2000);

      // 查找并勾选同意复选框（只有在成功选择计划后才会出现）
      console.log('🔍 查找并勾选同意复选框...');
      const agreementChecked = await this.checkAgreementCheckbox();
      if (!agreementChecked) {
        console.log('❌ 同意复选框勾选失败');
        this.updateStatus('同意复选框勾选失败', 'error');
        return false;
      }

      console.log('✅ 同意复选框勾选成功');

      // 等待一下让勾选生效
      await this.sleep(1000);

      // 点击Select Plan按钮
      console.log('🔍 开始查找Select Plan按钮...');
      const selectPlanClicked = await this.clickSelectPlanButton();
      if (!selectPlanClicked) {
        console.log('❌ Select Plan按钮点击失败');
        this.updateStatus('确认按钮点击失败', 'error');
        return false;
      }

      console.log('✅ Select Plan按钮点击成功');

      // 等待页面跳转回subscription页面
      console.log('⏳ 等待页面跳转回subscription页面...');
      const switchSuccess = await this.waitForPlanSwitchComplete();
      if (!switchSuccess) {
        console.log('❌ 计划切换失败或超时');
        this.updateStatus('计划切换失败', 'error');
        return false;
      }

      console.log('✅ 免费计划选择完成');
      this.updateStatus('免费计划选择完成', 'success');
      return true;

    } catch (error) {
      console.error('❌ 在弹出框中选择免费计划失败:', error);
      this.updateStatus('选择免费计划失败', 'error');
      return false;
    }
  }

  /**
   * 等待计划切换完成
   */
  private async waitForPlanSwitchComplete(): Promise<boolean> {
    console.log('⏳ 等待计划切换完成...');
    this.updateStatus('等待计划切换完成...', 'running');

    const maxWaitTime = 15000; // 15秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const currentUrl = window.location.href;
      console.log('📍 当前URL:', currentUrl);

      // 检查是否已经跳转回subscription页面
      if (currentUrl.includes('/account/subscription')) {
        console.log('✅ 已跳转回subscription页面');

        // 等待页面内容加载完成
        await this.sleep(2000);

        // 验证是否显示了免费计划
        const isFreePlanActive = await this.verifyFreePlanActive();
        if (isFreePlanActive) {
          console.log('✅ 免费计划切换成功');
          return true;
        } else {
          console.log('⚠️ 页面已跳转但免费计划状态未确认，继续等待...');
        }
      }

      // 检查是否出现了成功提示
      const pageText = document.body.textContent?.toLowerCase() || '';
      if (pageText.includes('plan updated') ||
          pageText.includes('subscription updated') ||
          pageText.includes('successfully') ||
          pageText.includes('计划已更新') ||
          pageText.includes('订阅已更新')) {
        console.log('✅ 检测到成功提示信息');
        await this.sleep(1000);
        return true;
      }

      // 检查是否模态框已关闭（另一个成功指标）
      const modals = document.querySelectorAll('[role="dialog"], .modal, [class*="modal"], [class*="popup"]');
      if (modals.length === 0) {
        console.log('✅ 模态框已关闭，可能切换成功');
        await this.sleep(1000);

        // 再次验证免费计划状态
        const isFreePlanActive = await this.verifyFreePlanActive();
        if (isFreePlanActive) {
          console.log('✅ 确认免费计划切换成功');
          return true;
        }
      }

      await this.sleep(500);
    }

    console.log('⏰ 等待计划切换完成超时');
    return false;
  }

  /**
   * 验证免费计划是否激活
   */
  private async verifyFreePlanActive(): Promise<boolean> {
    console.log('🔍 验证免费计划是否激活...');

    const pageText = document.body.textContent?.toLowerCase() || '';

    // 检查页面是否包含免费计划的标识
    const hasFreeIndicators = pageText.includes('community plan') ||
                             pageText.includes('free plan') ||
                             pageText.includes('current plan: free') ||
                             pageText.includes('current plan: community') ||
                             pageText.includes('$0') ||
                             pageText.includes('免费计划');

    if (hasFreeIndicators) {
      console.log('✅ 检测到免费计划标识');
      return true;
    }

    // 检查是否有"Change plan"按钮（说明已经在subscription页面）
    const hasChangePlanButton = pageText.includes('change plan') || pageText.includes('upgrade');
    if (hasChangePlanButton) {
      console.log('✅ 检测到Change plan按钮，说明在subscription页面');
      return true;
    }

    console.log('⚠️ 未检测到明确的免费计划标识');
    return false;
  }

  /**
   * 查找Free计划元素
   */
  private async findFreePlanElement(): Promise<HTMLElement | null> {
    const maxAttempts = 5;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`🔍 查找Free计划元素 (${attempt}/${maxAttempts})`);

      // 打印当前页面信息用于调试
      console.log('📄 当前页面URL:', window.location.href);
      console.log('📄 页面标题:', document.title);

      // 查找模态框
      const modals = document.querySelectorAll('[role="dialog"], .modal, [class*="modal"], [class*="popup"]');
      console.log('🔍 找到模态框数量:', modals.length);

      if (modals.length > 0) {
        const modal = modals[0];
        console.log('📋 模态框内容预览:', modal.textContent?.substring(0, 300));
      }

      // 方法1: 专门查找具有特定CSS类的计划卡片
      console.log('🎯 方法1: 查找plan-option-card类的元素...');
      const planOptionCards = Array.from(document.querySelectorAll('.plan-option-card, [class*="plan-option-card"]'));
      console.log('🔍 找到plan-option-card元素数量:', planOptionCards.length);

      for (const card of planOptionCards) {
        const text = card.textContent?.toLowerCase() || '';
        console.log('📋 计划卡片内容:', text.substring(0, 200));

        // 检查是否是免费计划
        if (text.includes('free') || text.includes('community') || text.includes('$0') || text.includes('免费')) {
          console.log('🎯 找到免费计划卡片:', card.className);
          return card as HTMLElement;
        }
      }

      // 方法2: 查找包含rt-BaseCard和clickable类的元素
      console.log('🎯 方法2: 查找rt-BaseCard clickable元素...');
      const baseCards = Array.from(document.querySelectorAll('.rt-BaseCard.clickable, [class*="rt-BaseCard"][class*="clickable"]'));
      console.log('🔍 找到rt-BaseCard clickable元素数量:', baseCards.length);

      for (const card of baseCards) {
        const text = card.textContent?.toLowerCase() || '';
        console.log('📋 BaseCard内容:', text.substring(0, 200));

        // 检查是否是免费计划
        if (text.includes('free') || text.includes('community') || text.includes('$0') || text.includes('免费')) {
          console.log('🎯 找到免费BaseCard:', card.className);
          return card as HTMLElement;
        }
      }

      // 方法3: 专门查找"Community Plan"文本
      console.log('🎯 方法3: 查找Community Plan文本...');
      const communityPlanElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.toLowerCase() || '';
        return text.includes('community plan') && text.includes('free');
      });

      console.log('🔍 找到Community Plan候选元素数量:', communityPlanElements.length);

      for (const element of communityPlanElements) {
        // 查找包含这个文本的最合适的容器
        let container = element;

        // 向上查找到合适的计划卡片容器
        for (let i = 0; i < 5; i++) {
          const parent = container.parentElement;
          if (!parent) break;

          const parentText = parent.textContent?.toLowerCase() || '';
          // 如果父元素包含完整的计划信息，使用父元素
          if (parentText.includes('community plan') &&
              parentText.includes('free') &&
              parentText.includes('user messages')) {
            container = parent;
          } else {
            break;
          }
        }

        console.log('🎯 找到Community Plan元素:', container.textContent?.substring(0, 150));
        return container as HTMLElement;
      }

      // 方法2: 查找包含"Free"文本的计划卡片
      console.log('🎯 方法2: 查找包含Free的计划卡片...');
      const modalElements = Array.from(document.querySelectorAll('[role="dialog"], .modal, [class*="modal"], [class*="popup"]'));
      for (const modal of modalElements) {
        // 查找所有可能的计划卡片
        const planCards = Array.from(modal.querySelectorAll('div')).filter(div => {
          const text = div.textContent?.toLowerCase() || '';
          const rect = div.getBoundingClientRect();

          // 必须包含关键信息且有合理的尺寸
          return text.includes('plan') &&
                 text.includes('free') &&
                 text.includes('user messages') &&
                 rect.width > 100 &&
                 rect.height > 50;
        });

        if (planCards.length > 0) {
          // 选择第一个（通常是免费计划）
          const firstCard = planCards[0];
          console.log('🎯 在模态框中找到免费计划卡片:', firstCard.textContent?.substring(0, 150));
          return firstCard as HTMLElement;
        }
      }

      // 方法3: 通过"Free"文本定位
      console.log('🎯 方法3: 通过Free文本定位...');
      const freeTextElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.trim() || '';
        return text === 'Free' || text === 'FREE';
      });

      for (const freeElement of freeTextElements) {
        // 查找包含这个"Free"文本的计划容器
        let container = freeElement;

        // 向上查找包含完整计划信息的容器
        for (let i = 0; i < 8; i++) {
          const parent = container.parentElement;
          if (!parent) break;

          const parentText = parent.textContent?.toLowerCase() || '';
          if (parentText.includes('plan') &&
              parentText.includes('user messages') &&
              parentText.includes('free')) {
            container = parent;
          } else if (i > 3) {
            // 如果已经向上查找了几层，停止
            break;
          }
        }

        console.log('🎯 通过Free文本找到计划容器:', container.textContent?.substring(0, 150));
        return container as HTMLElement;
      }

      // 方法4: 查找第一个计划选项（在Choose a Plan模态框中）
      console.log('🎯 方法4: 查找第一个计划选项...');
      const choosePlanModals = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.toLowerCase() || '';
        return text.includes('choose a plan');
      });

      for (const modal of choosePlanModals) {
        // 在这个模态框中查找所有计划选项
        const allDivs = Array.from(modal.querySelectorAll('div'));
        const planDivs = allDivs.filter(div => {
          const text = div.textContent?.toLowerCase() || '';
          const rect = div.getBoundingClientRect();

          return text.includes('plan') &&
                 text.includes('user messages') &&
                 rect.width > 200 &&
                 rect.height > 80;
        });

        if (planDivs.length > 0) {
          // 按照在页面中的位置排序，选择第一个
          planDivs.sort((a, b) => {
            const rectA = a.getBoundingClientRect();
            const rectB = b.getBoundingClientRect();
            return rectA.top - rectB.top;
          });

          const firstPlan = planDivs[0];
          console.log('🎯 找到第一个计划选项:', firstPlan.textContent?.substring(0, 150));
          return firstPlan as HTMLElement;
        }
      }

      // 方法5: 降低要求，查找任何包含"Free"的可点击元素
      console.log('🎯 方法5: 查找任何包含Free的可点击元素...');
      const freeElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.toLowerCase() || '';
        const rect = el.getBoundingClientRect();

        return text.includes('free') &&
               rect.width > 50 &&
               rect.height > 30 &&
               (el.tagName === 'BUTTON' ||
                el.tagName === 'DIV' ||
                el.getAttribute('role') === 'button' ||
                el.classList.contains('plan') ||
                el.classList.contains('card') ||
                (el as HTMLElement).style.cursor === 'pointer');
      });

      console.log('🔍 找到包含Free的可点击元素数量:', freeElements.length);

      if (freeElements.length > 0) {
        // 选择第一个
        const firstFreeElement = freeElements[0];
        console.log('🎯 选择第一个Free元素:', firstFreeElement.textContent?.substring(0, 150));
        return firstFreeElement as HTMLElement;
      }

      // 方法6: 最后的尝试 - 查找所有可能的计划元素
      console.log('🎯 方法6: 查找所有可能的计划元素...');
      const allPlanElements = Array.from(document.querySelectorAll('div, section, article')).filter(el => {
        const text = el.textContent?.toLowerCase() || '';
        const rect = el.getBoundingClientRect();

        return (text.includes('plan') || text.includes('tier') || text.includes('subscription')) &&
               rect.width > 100 &&
               rect.height > 80 &&
               text.length > 20 &&
               text.length < 500;
      });

      console.log('🔍 找到所有计划元素数量:', allPlanElements.length);

      if (allPlanElements.length > 0) {
        // 按位置排序，选择第一个（通常是免费的）
        allPlanElements.sort((a, b) => {
          const rectA = a.getBoundingClientRect();
          const rectB = b.getBoundingClientRect();
          return rectA.top - rectB.top || rectA.left - rectB.left;
        });

        const firstPlan = allPlanElements[0];
        console.log('🎯 选择第一个计划元素:', firstPlan.textContent?.substring(0, 150));
        return firstPlan as HTMLElement;
      }

      console.log(`⏳ 第${attempt}次查找失败，等待1秒后重试...`);
      await this.sleep(1000);
    }

    console.log('❌ 所有方法都未找到Free计划元素');
    console.log('📋 页面内容预览:', document.body.textContent?.substring(0, 500));
    return null;
  }

  /**
   * 点击Free计划元素
   */
  private async clickFreePlanElement(freePlanElement: HTMLElement): Promise<boolean> {
    try {
      console.log('🎯 开始点击免费计划元素...');
      console.log('📋 计划元素内容:', freePlanElement.textContent?.substring(0, 200));

      // 记录点击前的状态
      const beforeClickState = this.getElementState(freePlanElement);
      console.log('📊 点击前状态:', beforeClickState);

      // 确保元素可见和可点击
      const rect = freePlanElement.getBoundingClientRect();
      if (rect.width === 0 || rect.height === 0) {
        console.log('❌ 计划元素不可见');
        return false;
      }

      console.log('✅ 计划元素可见，准备点击');

      // 滚动到元素位置
      freePlanElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      await this.sleep(1000);

      // 多种点击策略
      const clickStrategies = [
        // 策略1: 直接点击
        () => {
          console.log('🎯 策略1: 直接点击计划元素');
          freePlanElement.click();
        },

        // 策略2: 模拟鼠标事件
        () => {
          console.log('🎯 策略2: 模拟鼠标点击事件');
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;

          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: centerX,
            clientY: centerY
          });

          freePlanElement.dispatchEvent(clickEvent);
        },

        // 策略3: 查找内部可点击元素
        () => {
          console.log('🎯 策略3: 查找内部可点击元素');
          const clickableElements = freePlanElement.querySelectorAll('button, [role="button"], input, [tabindex]');
          if (clickableElements.length > 0) {
            (clickableElements[0] as HTMLElement).click();
            return true;
          }
          return false;
        }
      ];

      // 尝试每种点击策略
      for (let i = 0; i < clickStrategies.length; i++) {
        try {
          console.log(`🎯 尝试点击策略 ${i + 1}...`);
          clickStrategies[i]();

          // 等待状态变化
          await this.sleep(1500);

          // 检查点击是否成功 - 使用更严格的验证
          const clickSuccess = await this.verifyPlanSelectionSuccess(freePlanElement, beforeClickState);
          if (clickSuccess) {
            console.log(`✅ 点击策略 ${i + 1} 成功！`);

            // 双重验证：再次检查是否真的有同意复选框或Select Plan按钮出现
            const doubleCheck = await this.doubleCheckPlanSelection();
            if (doubleCheck) {
              console.log(`✅ 双重验证通过，点击确实成功！`);
              return true;
            } else {
              console.log(`⚠️ 双重验证失败，点击可能没有真正成功`);
            }
          }

          console.log(`⚠️ 点击策略 ${i + 1} 未检测到成功状态`);

        } catch (error) {
          console.log(`❌ 点击策略 ${i + 1} 失败:`, error);
        }
      }

      console.log('❌ 所有点击策略都失败了');
      return false;

    } catch (error) {
      console.error('❌ 点击免费计划失败:', error);
      return false;
    }
  }

  /**
   * 获取元素状态信息
   */
  private getElementState(element: HTMLElement): any {
    return {
      className: element.className,
      ariaSelected: element.getAttribute('aria-selected'),
      style: element.style.cssText,
      hasSelectedClass: element.classList.contains('selected'),
      hasActiveClass: element.classList.contains('active'),
      backgroundColor: window.getComputedStyle(element).backgroundColor,
      borderColor: window.getComputedStyle(element).borderColor
    };
  }

  /**
   * 双重验证计划选择是否成功
   */
  private async doubleCheckPlanSelection(): Promise<boolean> {
    console.log('🔍 执行双重验证...');

    // 等待更长时间确保页面完全响应
    await this.sleep(3000);

    // 检查1: 同意复选框是否出现
    const agreementCheckbox = this.findAgreementCheckbox();
    if (agreementCheckbox) {
      console.log('✅ 双重验证：找到同意复选框');
      return true;
    }

    // 检查2: Select Plan按钮是否出现
    const selectPlanButton = this.findSelectPlanButton();
    if (selectPlanButton) {
      console.log('✅ 双重验证：找到Select Plan按钮');
      return true;
    }

    // 检查3: 页面是否出现了确认相关的文本
    const pageText = document.body.textContent?.toLowerCase() || '';
    const hasConfirmationText = pageText.includes('select plan') &&
                               pageText.includes('agree') &&
                               pageText.includes('terms');

    if (hasConfirmationText) {
      console.log('✅ 双重验证：页面包含确认相关文本');
      return true;
    }

    // 检查4: 是否有新的模态框或弹窗出现
    const modals = Array.from(document.querySelectorAll('[role="dialog"], .modal, [class*="modal"], [class*="popup"]'));
    for (const modal of modals) {
      const modalText = modal.textContent?.toLowerCase() || '';
      if (modalText.includes('select plan') || modalText.includes('confirm plan')) {
        console.log('✅ 双重验证：找到确认模态框');
        return true;
      }
    }

    console.log('❌ 双重验证失败：没有找到任何成功指标');
    console.log('📋 当前页面内容预览:', pageText.substring(0, 500));
    return false;
  }

  /**
   * 验证计划选择是否成功
   */
  private async verifyPlanSelectionSuccess(element: HTMLElement, beforeState: any): Promise<boolean> {
    try {
      console.log('🔍 验证计划选择是否成功...');

      // 获取当前状态
      const afterState = this.getElementState(element);
      console.log('📊 点击后状态:', afterState);

      // 检查状态变化
      const stateChanged =
        afterState.className !== beforeState.className ||
        afterState.ariaSelected !== beforeState.ariaSelected ||
        afterState.style !== beforeState.style ||
        afterState.backgroundColor !== beforeState.backgroundColor ||
        afterState.borderColor !== beforeState.borderColor;

      if (stateChanged) {
        console.log('✅ 检测到元素状态变化');
      }

      // 检查选中状态指示器
      const hasSelectionIndicators =
        element.classList.contains('selected') ||
        element.classList.contains('active') ||
        element.getAttribute('aria-selected') === 'true' ||
        element.querySelector('.selected, .active, [aria-selected="true"]') !== null;

      if (hasSelectionIndicators) {
        console.log('✅ 检测到选中状态指示器');
      }

      // 检查是否出现了同意复选框（这是成功选择的关键指标）
      console.log('🔍 等待同意复选框出现...');
      await this.sleep(2000); // 等待复选框出现

      const agreementCheckbox = this.findAgreementCheckbox();
      if (agreementCheckbox) {
        console.log('✅ 检测到同意复选框出现，计划选择成功');
        return true;
      }

      // 检查是否出现了"Select Plan"按钮（另一个成功指标）
      console.log('🔍 检查是否出现Select Plan按钮...');
      const selectPlanButton = this.findSelectPlanButton();
      if (selectPlanButton) {
        console.log('✅ 检测到Select Plan按钮出现，计划选择成功');
        return true;
      }

      // 检查是否出现了新的模态框或内容变化
      console.log('🔍 检查页面内容变化...');
      const currentPageText = document.body.textContent?.toLowerCase() || '';

      // 检查是否出现了计划选择相关的新内容
      const hasNewSelectContent = currentPageText.includes('select plan') ||
                                 currentPageText.includes('confirm plan') ||
                                 currentPageText.includes('agree to') ||
                                 currentPageText.includes('terms and conditions');

      // 检查是否有新的模态框出现
      const modals = document.querySelectorAll('[role="dialog"], .modal, [class*="modal"], [class*="popup"]');
      let hasNewModalContent = false;

      if (modals.length > 0) {
        const modal = modals[0];
        const modalText = modal.textContent?.toLowerCase() || '';

        // 检查模态框是否包含选择确认相关内容
        hasNewModalContent = modalText.includes('select plan') ||
                           modalText.includes('confirm') ||
                           modalText.includes('agree') ||
                           modalText.includes('terms');

        console.log('📋 当前模态框内容:', modalText.substring(0, 300));
      }

      if (hasNewSelectContent && hasNewModalContent) {
        console.log('✅ 检测到页面内容发生了预期变化');
        return true;
      }

      console.log('❌ 未检测到任何成功指标：');
      console.log('  - 没有找到同意复选框');
      console.log('  - 没有找到Select Plan按钮');
      console.log('  - 模态框内容未发生预期变化');
      console.log('  - 元素状态变化:', stateChanged);
      console.log('  - 选中状态指示器:', hasSelectionIndicators);

      return false;

    } catch (error) {
      console.error('❌ 验证计划选择状态失败:', error);
      return false;
    }
  }

  /**
   * 查找同意复选框
   */
  private findAgreementCheckbox(): HTMLElement | null {
    // 查找包含同意相关文本的复选框
    const checkboxes = Array.from(document.querySelectorAll('input[type="checkbox"]'));

    for (const checkbox of checkboxes) {
      const label = checkbox.closest('label') ||
                   document.querySelector(`label[for="${checkbox.id}"]`) ||
                   checkbox.parentElement;

      if (label) {
        const text = label.textContent?.toLowerCase() || '';
        if (text.includes('agree') || text.includes('terms') ||
            text.includes('同意') || text.includes('条款')) {
          return checkbox as HTMLElement;
        }
      }
    }

    // 查找角色为checkbox的元素
    const roleCheckboxes = Array.from(document.querySelectorAll('[role="checkbox"]'));
    for (const checkbox of roleCheckboxes) {
      const text = checkbox.textContent?.toLowerCase() || '';
      const parentText = checkbox.parentElement?.textContent?.toLowerCase() || '';

      if (text.includes('agree') || text.includes('terms') ||
          parentText.includes('agree') || parentText.includes('terms') ||
          text.includes('同意') || text.includes('条款') ||
          parentText.includes('同意') || parentText.includes('条款')) {
        return checkbox as HTMLElement;
      }
    }

    return null;
  }

  /**
   * 勾选同意复选框
   */
  private async checkAgreementCheckbox(): Promise<boolean> {
    try {
      console.log('🔍 查找同意复选框...');

      const maxAttempts = 5;
      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        console.log(`🔍 第${attempt}次查找同意复选框...`);

        const checkbox = this.findAgreementCheckbox();
        if (!checkbox) {
          console.log('⏳ 未找到同意复选框，等待1秒后重试...');
          await this.sleep(1000);
          continue;
        }

        console.log('✅ 找到同意复选框:', checkbox);

        // 检查是否已经勾选
        const isChecked = (checkbox as HTMLInputElement).checked ||
                         checkbox.getAttribute('aria-checked') === 'true' ||
                         checkbox.classList.contains('checked');

        if (isChecked) {
          console.log('✅ 同意复选框已经勾选');
          return true;
        }

        // 点击勾选
        console.log('🎯 点击勾选同意复选框...');
        checkbox.click();
        await this.sleep(500);

        // 验证勾选成功
        const isNowChecked = (checkbox as HTMLInputElement).checked ||
                            checkbox.getAttribute('aria-checked') === 'true' ||
                            checkbox.classList.contains('checked');

        if (isNowChecked) {
          console.log('✅ 同意复选框勾选成功');
          return true;
        }

        console.log('⚠️ 复选框点击后未检测到勾选状态，继续尝试...');
      }

      console.log('❌ 多次尝试后仍无法勾选同意复选框');
      return false;

    } catch (error) {
      console.error('❌ 勾选同意复选框失败:', error);
      return false;
    }
  }

  /**
   * 查找Select Plan按钮（不点击，只查找）
   */
  private findSelectPlanButton(): HTMLElement | null {
    console.log('🔍 查找Select Plan按钮...');

    // 方法1: 查找包含特定文本的按钮
    const buttonTexts = ['select plan', 'select', 'choose plan', 'choose', 'confirm', 'continue', '选择计划', '确认', '继续'];
    const allButtons = Array.from(document.querySelectorAll('button, [role="button"], a[href*="select"], input[type="submit"], input[type="button"]'));

    for (const button of allButtons) {
      const text = button.textContent?.toLowerCase().trim() || '';
      const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
      const value = (button as HTMLInputElement).value?.toLowerCase() || '';

      for (const buttonText of buttonTexts) {
        if (text.includes(buttonText) || ariaLabel.includes(buttonText) || value.includes(buttonText)) {
          // 确保按钮是可见的
          const rect = button.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            console.log('✅ 找到Select Plan按钮:', text || ariaLabel || value);
            return button as HTMLElement;
          }
        }
      }
    }

    // 方法2: 查找模态框底部的主要按钮
    const modals = Array.from(document.querySelectorAll('[role="dialog"], .modal, [class*="modal"]'));
    for (const modal of modals) {
      const modalButtons = Array.from(modal.querySelectorAll('button, [role="button"]'));

      // 查找主要按钮（通常在底部）
      const primaryButtons = modalButtons.filter(btn => {
        const classList = btn.className.toLowerCase();
        return classList.includes('primary') || classList.includes('main') ||
               classList.includes('submit') || classList.includes('confirm');
      });

      if (primaryButtons.length > 0) {
        const rect = primaryButtons[0].getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          console.log('✅ 找到模态框中的主要按钮');
          return primaryButtons[0] as HTMLElement;
        }
      }

      // 如果没有主要按钮，取最后一个按钮（通常是确认按钮）
      if (modalButtons.length > 0) {
        const lastButton = modalButtons[modalButtons.length - 1];
        const rect = lastButton.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          console.log('✅ 找到模态框中的最后一个按钮');
          return lastButton as HTMLElement;
        }
      }
    }

    console.log('❌ 未找到Select Plan按钮');
    return null;
  }

  /**
   * 点击Select Plan按钮
   */
  private async clickSelectPlanButton(): Promise<boolean> {
    const maxAttempts = 5;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`🔍 查找Select Plan按钮 (${attempt}/${maxAttempts})`);

      // 方法1: 查找包含特定文本的按钮
      const buttonTexts = ['select plan', 'select', 'choose plan', 'choose', 'confirm', 'continue', '选择计划', '确认', '继续'];
      const allButtons = Array.from(document.querySelectorAll('button, [role="button"], a[href*="select"], input[type="submit"], input[type="button"]'));

      for (const button of allButtons) {
        const text = button.textContent?.toLowerCase().trim() || '';
        const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
        const value = (button as HTMLInputElement).value?.toLowerCase() || '';

        for (const buttonText of buttonTexts) {
          if (text.includes(buttonText) || ariaLabel.includes(buttonText) || value.includes(buttonText)) {
            // 确保按钮是可见和可点击的
            const rect = button.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              console.log('🎯 找到Select Plan按钮:', text || ariaLabel || value);
              (button as HTMLElement).click();
              await this.sleep(1000);
              return true;
            }
          }
        }
      }

      // 方法2: 查找模态框底部的主要按钮
      const modals = Array.from(document.querySelectorAll('[role="dialog"], .modal, [class*="modal"]'));
      for (const modal of modals) {
        const modalButtons = Array.from(modal.querySelectorAll('button, [role="button"]'));

        // 查找主要按钮（通常在底部）
        const primaryButtons = modalButtons.filter(btn => {
          const classList = btn.className.toLowerCase();
          return classList.includes('primary') || classList.includes('main') ||
                 classList.includes('submit') || classList.includes('confirm');
        });

        if (primaryButtons.length > 0) {
          console.log('🎯 找到模态框中的主要按钮');
          (primaryButtons[0] as HTMLElement).click();
          await this.sleep(1000);
          return true;
        }

        // 如果没有主要按钮，取最后一个按钮（通常是确认按钮）
        if (modalButtons.length > 0) {
          const lastButton = modalButtons[modalButtons.length - 1];
          const text = lastButton.textContent?.toLowerCase() || '';
          if (!text.includes('cancel') && !text.includes('close') && !text.includes('取消')) {
            console.log('🎯 找到模态框中的最后一个按钮（非取消按钮）');
            (lastButton as HTMLElement).click();
            await this.sleep(1000);
            return true;
          }
        }
      }

      // 方法3: 查找表单提交按钮
      const forms = Array.from(document.querySelectorAll('form'));
      for (const form of forms) {
        const submitButtons = Array.from(form.querySelectorAll('input[type="submit"], button[type="submit"], button:not([type])'));
        if (submitButtons.length > 0) {
          console.log('🎯 找到表单提交按钮');
          (submitButtons[0] as HTMLElement).click();
          await this.sleep(1000);
          return true;
        }
      }

      console.log(`⏳ 第${attempt}次查找失败，等待1秒后重试...`);
      await this.sleep(1000);
    }

    console.log('❌ 未找到Select Plan按钮');
    return false;
  }

  /**
   * 等待返回到订阅页面
   */
  private async waitForSubscriptionPage(): Promise<void> {
    console.log('⏳ 等待返回到订阅页面...');

    const maxWaitTime = 15000; // 15秒
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const url = window.location.href.toLowerCase();

      // 检查是否返回到订阅页面
      if (url.includes('/account/subscription')) {
        // 检查是否显示了Community Plan
        const pageText = document.body.textContent?.toLowerCase() || '';
        if (pageText.includes('community plan') || pageText.includes('current plan')) {
          console.log('✅ 已返回到订阅页面，显示Community Plan');
          return;
        }
      }

      await this.sleep(1000);
    }

    console.log('⏰ 等待返回订阅页面超时');
  }
}

// 初始化Content Script（在所有augmentcode.com域名上）
if (window.location.hostname.includes('augmentcode.com')) {
  console.log('🎯 检测到Augment Code页面，启动Content Script');
  console.log('当前域名:', window.location.hostname);
  new RegistrationContentScript();
}
