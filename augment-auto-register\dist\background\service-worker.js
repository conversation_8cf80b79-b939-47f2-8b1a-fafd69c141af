/**
 * Service Worker - Chrome扩展后台服务脚本
 * 处理消息路由、状态管理和任务调度
 */

import { EmailService } from '../lib/email-service.js';
import { EmailConfigManager } from '../lib/config-manager.js';
import { WebEmailMonitor } from '../lib/email-monitor.js';
import { MessageType, ErrorType, EmailServiceError } from '../lib/types.js';

class ServiceWorkerController {
  constructor() {
    this.emailService = null;
    this.configManager = null;
    this.emailMonitor = null;
    this.isInitialized = false;
    
    // QQ邮箱提供商配置
    this.qqProvider = {
      name: 'QQ邮箱',
      type: 'qq',
      webUrl: 'https://mail.qq.com',
      selectors: {
        loginButton: '#login_button',
        emailList: '.mail_list_item',
        emailContent: '.mail_content',
        refreshButton: '.refresh_btn'
      },
      rateLimit: {
        requests: 10,
        window: 60000
      }
    };
    
    this.initialize();
    this.setupMessageHandlers();
    this.setupEventHandlers();
  }

  /**
   * 初始化服务
   */
  async initialize() {
    try {
      console.log('Service Worker 初始化开始...');
      
      // 初始化配置管理器
      this.configManager = new EmailConfigManager();
      await this.configManager.initialize();
      
      // 初始化邮箱服务
      this.emailService = new EmailService();
      await this.emailService.initialize();
      
      // 初始化邮箱监控器
      this.emailMonitor = new WebEmailMonitor(this.qqProvider);
      
      this.isInitialized = true;
      console.log('Service Worker 初始化完成');
      
      // 设置扩展图标状态
      await this.updateExtensionIcon('ready');
      
    } catch (error) {
      console.error('Service Worker 初始化失败:', error);
      await this.updateExtensionIcon('error');
      throw error;
    }
  }

  /**
   * 设置消息处理器
   */
  setupMessageHandlers() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // 扩展安装事件
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstalled(details);
    });

    // 扩展启动事件
    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });

    // 标签页更新事件
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });

    // 监听SOCKS5代理认证请求
    if (chrome.webRequest && chrome.webRequest.onAuthRequired) {
      chrome.webRequest.onAuthRequired.addListener(
        this.handleProxyAuth.bind(this),
        { urls: ['<all_urls>'] },
        ['blocking']
      );
      console.log('✅ SOCKS5代理认证监听器已设置');
    }

    console.log('✅ 事件处理器设置完成');
  }

  /**
   * 处理消息
   */
  async handleMessage(message, sender, sendResponse) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log('收到消息:', message.type);

      switch (message.type) {
        case MessageType.GENERATE_EMAIL:
          await this.handleGenerateEmail(sendResponse);
          break;
          
        case MessageType.MONITOR_EMAIL:
          await this.handleMonitorEmail(message.data, sendResponse);
          break;
          
        case 'stopMonitoring':
          await this.handleStopMonitoring(sendResponse);
          break;
          
        case 'startRegistration':
          await this.handleStartRegistration(sendResponse);
          break;
          
        case MessageType.GET_STATISTICS:
          await this.handleGetStatistics(sendResponse);
          break;
          
        case 'getNextEmail':
          await this.handleGetNextEmail(sendResponse);
          break;
          
        case 'exportData':
          await this.handleExportData(sendResponse);
          break;
          
        case 'updateConfig':
          await this.handleUpdateConfig(message.data, sendResponse);
          break;

        case 'REGISTRATION_SUCCESS':
          await this.handleRegistrationSuccess(message.data, sendResponse);
          break;

        case 'READY_FOR_NEXT_REGISTRATION':
          await this.handleReadyForNextRegistration(sendResponse);
          break;

        case 'GET_REGISTRATION_RECORDS':
          await this.handleGetRegistrationRecords(sendResponse);
          break;

        case 'CLEAR_BROWSER_TRACES':
          await this.handleClearBrowserTraces(message.data, sendResponse);
          break;

        case 'SWITCH_PROXY':
          await this.handleSwitchProxy(sendResponse);
          break;

        case 'getProxySettings':
          await this.handleGetProxySettings(sendResponse);
          break;

        case 'OPEN_IN_INCOGNITO':
          await this.handleOpenInIncognito(message.data, sendResponse);
          break;

        default:
          sendResponse({
            success: false,
            error: `未知的消息类型: ${message.type}`
          });
      }
      
    } catch (error) {
      console.error('处理消息失败:', error);
      sendResponse({
        success: false,
        error: error.message || '处理消息时发生错误'
      });
    }
  }

  /**
   * 处理生成邮箱请求
   */
  async handleGenerateEmail(sendResponse) {
    try {
      const emailInfo = await this.emailService.generateEmail();
      
      sendResponse({
        success: true,
        data: emailInfo
      });
      
      // 更新扩展图标徽章
      await this.updateBadge(emailInfo.counter.toString());
      
    } catch (error) {
      console.error('生成邮箱失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理邮箱监控请求
   */
  async handleMonitorEmail(emailInfo, sendResponse) {
    try {
      console.log('🔍 开始监控邮箱:', emailInfo.address);
      console.log('🔍 邮箱信息:', emailInfo);
      console.log('🔍 监控超时配置:', this.configManager.getConfig().monitorTimeout);

      // 更新扩展图标状态
      await this.updateExtensionIcon('monitoring');

      // 开始监控
      console.log('🔍 调用emailMonitor.startMonitoring...');
      const verificationCode = await this.emailMonitor.startMonitoring(
        emailInfo,
        this.configManager.getConfig().monitorTimeout
      );
      console.log('🔍 emailMonitor.startMonitoring 返回结果:', verificationCode);
      
      if (verificationCode) {
        // 记录成功
        await this.configManager.recordSuccessfulRegistration(emailInfo.address);
        
        sendResponse({
          success: true,
          verificationCode: verificationCode
        });
        
        console.log('监控成功，获取到验证码:', verificationCode);
      } else {
        sendResponse({
          success: true,
          verificationCode: null
        });
        
        console.log('监控超时，未获取到验证码');
      }
      
    } catch (error) {
      console.error('邮箱监控失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    } finally {
      // 恢复扩展图标状态
      await this.updateExtensionIcon('ready');
    }
  }

  /**
   * 处理停止监控请求
   */
  async handleStopMonitoring(sendResponse) {
    try {
      if (this.emailMonitor) {
        this.emailMonitor.stopMonitoring();
      }
      
      await this.updateExtensionIcon('ready');
      
      sendResponse({
        success: true
      });
      
    } catch (error) {
      console.error('停止监控失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理开始注册请求
   */
  async handleStartRegistration(sendResponse) {
    try {
      console.log('🚀 开始处理注册请求...');

      // 生成邮箱
      const emailInfo = await this.emailService.generateEmail();
      console.log('📧 生成邮箱:', emailInfo.address);

      // 获取当前活动标签页
      const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

      // 检查当前页面是否为注册页面
      if (currentTab && currentTab.url && currentTab.url.includes('login.augmentcode.com')) {
        console.log('✅ 在当前注册页面执行自动注册');

        // 直接在当前页面执行注册
        setTimeout(async () => {
          try {
            await chrome.tabs.sendMessage(currentTab.id, {
              type: 'startAutoRegistration',
              data: {
                email: emailInfo.address,
                autoMonitor: true
              }
            });
            console.log('✅ 自动注册消息已发送');
          } catch (error) {
            console.warn('❌ 发送自动注册消息失败:', error);
          }
        }, 2000); // 减少等待时间到2秒

        sendResponse({
          success: true,
          data: {
            emailInfo,
            tabId: currentTab.id,
            message: '在当前页面开始注册'
          }
        });

      } else {
        console.log('🔄 当前页面不是注册页面，打开新标签页');

        // 如果不在注册页面，打开新标签页
        const tab = await chrome.tabs.create({
          url: 'https://login.augmentcode.com/',
          active: true
        });

        // 等待页面加载后开始自动填充
        setTimeout(async () => {
          try {
            await chrome.tabs.sendMessage(tab.id, {
              type: 'startAutoRegistration',
              data: {
                email: emailInfo.address,
                autoMonitor: true
              }
            });
            console.log('✅ 自动注册消息已发送到新标签页');
          } catch (error) {
            console.warn('❌ 发送自动注册消息失败:', error);
          }
        }, 3000);

        sendResponse({
          success: true,
          data: {
            emailInfo,
            tabId: tab.id,
            message: '已打开新标签页开始注册'
          }
        });
      }
      
    } catch (error) {
      console.error('开始注册失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理获取统计信息请求
   */
  async handleGetStatistics(sendResponse) {
    try {
      const statistics = this.configManager.getStatistics();
      const successRate = this.configManager.getSuccessRate();
      
      sendResponse({
        success: true,
        data: {
          ...statistics,
          successRate
        }
      });
      
    } catch (error) {
      console.error('获取统计信息失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理获取下一个邮箱请求
   */
  async handleGetNextEmail(sendResponse) {
    try {
      const nextEmail = this.configManager.getNextEmail();
      
      sendResponse({
        success: true,
        data: nextEmail
      });
      
    } catch (error) {
      console.error('获取下一个邮箱失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理导出数据请求
   */
  async handleExportData(sendResponse) {
    try {
      const config = this.configManager.exportConfig();
      const statistics = this.configManager.getStatistics();
      
      const exportData = {
        config: JSON.parse(config),
        statistics,
        exportTime: new Date().toISOString(),
        version: '2.0'
      };
      
      sendResponse({
        success: true,
        data: exportData
      });
      
    } catch (error) {
      console.error('导出数据失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理更新配置请求
   */
  async handleUpdateConfig(configData, sendResponse) {
    try {
      await this.configManager.updateConfig(configData);

      sendResponse({
        success: true
      });

    } catch (error) {
      console.error('更新配置失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理扩展安装事件
   */
  async handleInstalled(details) {
    console.log('扩展已安装:', details.reason);

    if (details.reason === 'install') {
      // 首次安装
      console.log('首次安装扩展');

      // 设置默认配置
      await this.initialize();

      // 显示欢迎页面
      chrome.tabs.create({
        url: chrome.runtime.getURL('options/options.html')
      });

    } else if (details.reason === 'update') {
      // 扩展更新
      console.log('扩展已更新');

      // 重新初始化
      await this.initialize();
    }
  }

  /**
   * 处理扩展启动事件
   */
  async handleStartup() {
    console.log('扩展启动');
    await this.initialize();
  }

  /**
   * 处理标签页更新事件
   */
  handleTabUpdated(tabId, changeInfo, tab) {
    // 检查是否是目标网站
    if (changeInfo.status === 'complete' && tab.url) {
      if (tab.url.includes('login.augmentcode.com')) {
        console.log('检测到注册页面加载完成');
        // 可以在这里注入content script或发送消息
      } else if (tab.url.includes('mail.qq.com')) {
        console.log('检测到QQ邮箱页面加载完成');
        // QQ邮箱页面加载完成
      }
    }
  }

  /**
   * 更新扩展图标状态
   */
  async updateExtensionIcon(status) {
    try {
      const iconPaths = {
        ready: {
          '16': 'assets/icons/icon16.png',
          '32': 'assets/icons/icon32.png',
          '48': 'assets/icons/icon48.png',
          '128': 'assets/icons/icon128.png'
        },
        monitoring: {
          '16': 'assets/icons/icon16-monitoring.png',
          '32': 'assets/icons/icon32-monitoring.png',
          '48': 'assets/icons/icon48-monitoring.png',
          '128': 'assets/icons/icon128-monitoring.png'
        },
        error: {
          '16': 'assets/icons/icon16-error.png',
          '32': 'assets/icons/icon32-error.png',
          '48': 'assets/icons/icon48-error.png',
          '128': 'assets/icons/icon128-error.png'
        }
      };

      const icons = iconPaths[status] || iconPaths.ready;

      await chrome.action.setIcon({ path: icons });

      // 设置工具提示
      const tooltips = {
        ready: 'Augment 注册助手 - 就绪',
        monitoring: 'Augment 注册助手 - 监控中',
        error: 'Augment 注册助手 - 错误'
      };

      await chrome.action.setTitle({ title: tooltips[status] || tooltips.ready });

    } catch (error) {
      console.warn('更新扩展图标失败:', error);
    }
  }

  /**
   * 更新扩展徽章
   */
  async updateBadge(text, color = '#667eea') {
    try {
      await chrome.action.setBadgeText({ text: text.toString() });
      await chrome.action.setBadgeBackgroundColor({ color });
    } catch (error) {
      console.warn('更新扩展徽章失败:', error);
    }
  }

  /**
   * 清除扩展徽章
   */
  async clearBadge() {
    try {
      await chrome.action.setBadgeText({ text: '' });
    } catch (error) {
      console.warn('清除扩展徽章失败:', error);
    }
  }

  /**
   * 发送通知
   */
  async sendNotification(title, message, type = 'basic') {
    try {
      const notificationId = `augment-${Date.now()}`;

      await chrome.notifications.create(notificationId, {
        type: type,
        iconUrl: 'assets/icons/icon48.png',
        title: title,
        message: message
      });

      // 自动清除通知
      setTimeout(() => {
        chrome.notifications.clear(notificationId);
      }, 5000);

    } catch (error) {
      console.warn('发送通知失败:', error);
    }
  }

  /**
   * 记录操作日志
   */
  async logOperation(operation, details = {}) {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        operation,
        details,
        userAgent: navigator.userAgent
      };

      // 获取现有日志
      const result = await chrome.storage.local.get(['operationLogs']);
      const logs = result.operationLogs || [];

      // 添加新日志
      logs.push(logEntry);

      // 限制日志数量（保留最近100条）
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      // 保存日志
      await chrome.storage.local.set({ operationLogs: logs });

    } catch (error) {
      console.warn('记录操作日志失败:', error);
    }
  }

  /**
   * 处理注册成功记录
   */
  async handleRegistrationSuccess(userInfo, sendResponse) {
    try {
      console.log('📝 处理注册成功记录:', userInfo);

      // 获取当前的成功记录
      const result = await chrome.storage.local.get(['registrationSuccesses']);
      const successes = result.registrationSuccesses || [];

      // 添加新的成功记录
      const successRecord = {
        id: Date.now().toString(),
        email: userInfo.email,
        registrationTime: userInfo.registrationTime,
        successUrl: userInfo.successUrl,
        userAgent: userInfo.userAgent,
        timestamp: new Date().toISOString()
      };

      successes.push(successRecord);

      // 保存到存储
      await chrome.storage.local.set({ registrationSuccesses: successes });

      console.log('✅ 注册成功记录已保存:', successRecord);

      // 记录操作日志
      await this.logOperation('REGISTRATION_SUCCESS', {
        email: userInfo.email,
        successUrl: userInfo.successUrl
      });

      sendResponse({
        success: true,
        data: successRecord
      });

    } catch (error) {
      console.error('❌ 处理注册成功记录失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理准备下一次注册
   */
  async handleReadyForNextRegistration(sendResponse) {
    try {
      console.log('🔄 准备开始下一次注册...');

      // 等待一段时间确保页面完全加载
      setTimeout(async () => {
        try {
          // 获取下一个邮箱
          const nextEmailResult = await this.handleGetNextEmail(() => {});

          if (nextEmailResult && nextEmailResult.success) {
            console.log('✅ 已准备好下一次注册，邮箱:', nextEmailResult.data.address);

            // 可以在这里触发自动开始下一次注册
            // 或者只是通知前端准备就绪
            await this.logOperation('READY_FOR_NEXT', {
              nextEmail: nextEmailResult.data.address
            });
          }

        } catch (error) {
          console.error('❌ 准备下一次注册失败:', error);
        }
      }, 3000); // 等待3秒

      sendResponse({
        success: true,
        message: '正在准备下一次注册...'
      });

    } catch (error) {
      console.error('❌ 处理准备下一次注册失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 获取注册记录
   */
  async handleGetRegistrationRecords(sendResponse) {
    try {
      console.log('📋 获取注册记录...');

      // 从Chrome存储获取注册记录
      const result = await chrome.storage.local.get(['registrationSuccesses']);
      const records = result.registrationSuccesses || [];

      console.log(`✅ 获取到 ${records.length} 条注册记录`);

      sendResponse({
        success: true,
        data: records
      });

    } catch (error) {
      console.error('❌ 获取注册记录失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 清理浏览器痕迹
   */
  async handleClearBrowserTraces(data, sendResponse) {
    try {
      console.log('🧹 开始清理浏览器痕迹...');
      const domain = data.domain || 'augmentcode.com';

      // 1. 清理所有相关域名的cookies
      console.log('🍪 开始清理cookies...');
      const cookieDomains = [domain, `.${domain}`];
      let totalCookies = 0;
      const cookieDetails = [];

      for (const cookieDomain of cookieDomains) {
        try {
          console.log(`🔍 检查域名: ${cookieDomain}`);
          const cookies = await chrome.cookies.getAll({
            domain: cookieDomain
          });

          console.log(`📊 找到 ${cookies.length} 个cookies (域名: ${cookieDomain})`);

          for (const cookie of cookies) {
            try {
              await chrome.cookies.remove({
                url: `https://${cookie.domain}${cookie.path}`,
                name: cookie.name
              });
              cookieDetails.push(`${cookie.name} (${cookie.domain})`);
            } catch (removeError) {
              console.warn(`删除cookie失败: ${cookie.name}`, removeError);
            }
          }
          totalCookies += cookies.length;
        } catch (error) {
          console.warn(`清理域名 ${cookieDomain} 的cookies失败:`, error);
        }
      }

      console.log(`✅ Cookies清理完成: 共清理 ${totalCookies} 个cookies`);
      if (cookieDetails.length > 0) {
        console.log('📋 清理的cookies详情:', cookieDetails);
      }

      // 2. 清理扩展存储数据
      console.log('💾 开始清理扩展存储数据...');

      const storageKeysToRemove = [
        'currentEmail',
        'registrationState',
        'emailInfo',
        'verificationCode',
        'registrationRecords',
        'currentProxy'
      ];

      console.log('🔍 检查本地存储中的数据...');
      const beforeStorage = await chrome.storage.local.get(null);
      console.log('📊 清理前本地存储项目数:', Object.keys(beforeStorage).length);

      await chrome.storage.local.remove(storageKeysToRemove);
      console.log('✅ 本地存储指定项目已清理:', storageKeysToRemove);

      console.log('🔍 检查会话存储...');
      const beforeSession = await chrome.storage.session.get(null);
      console.log('📊 清理前会话存储项目数:', Object.keys(beforeSession).length);

      await chrome.storage.session.clear();
      console.log('✅ 会话存储已完全清理');

      // 验证清理结果
      const afterStorage = await chrome.storage.local.get(null);
      const afterSession = await chrome.storage.session.get(null);
      console.log('📊 清理后本地存储项目数:', Object.keys(afterStorage).length);
      console.log('📊 清理后会话存储项目数:', Object.keys(afterSession).length);
      console.log('✅ 扩展存储数据清理完成');

      // 3. 清理浏览器数据（缓存、localStorage等）
      console.log('🗂️ 开始清理浏览器数据...');

      if (chrome.browsingData) {
        const origins = [
          `https://${domain}`,
          `https://*.${domain}`,
          `http://${domain}`,
          `http://*.${domain}`
        ];

        console.log('🎯 清理目标域名:', origins);

        const dataTypes = {
          cache: true,
          localStorage: true,
          // sessionStorage: true, // ❌ 不支持，移除
          indexedDB: true,
          webSQL: true,
          downloads: false, // 不清理下载记录
          history: false,   // 不清理历史记录
          passwords: false, // 不清理密码
          formData: true,   // 清理表单数据
          cookies: true     // 再次确保清理cookies
        };

        console.log('🧹 清理数据类型:', Object.entries(dataTypes).filter(([k, v]) => v).map(([k]) => k));

        await chrome.browsingData.remove(
          {
            origins: origins,
            since: 0 // 清理所有时间的数据
          },
          dataTypes
        );

        console.log('✅ 浏览器数据清理完成');
        console.log('📋 已清理: 缓存、localStorage、indexedDB、webSQL、表单数据、cookies');
        console.log('ℹ️ 注意: sessionStorage 通过页面级清理处理');
      } else {
        console.warn('⚠️ chrome.browsingData API不可用，跳过浏览器数据清理');
      }

      // 4. 等待一下确保清理完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      sendResponse({
        success: true,
        message: `浏览器痕迹清理完成，共清理 ${totalCookies} 个cookies`
      });

    } catch (error) {
      console.error('❌ 清理浏览器痕迹失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 切换代理服务器
   */
  async handleSwitchProxy(sendResponse) {
    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 开始获取新代理... (尝试 ${attempt}/${maxRetries})`);

        // 调用熊猫代理API获取新SOCKS5代理 (带账号密码)
        const proxyApiUrl = 'http://route.xiongmaodaili.com/xiongmao-web/api/glip?secret=6c54554463bd0fc23865e85b3d6e45af&orderNo=GL202507240930417sRetlJ3&count=1&isTxt=1&proxyType=2&returnAccount=2';

        const response = await fetch(proxyApiUrl, {
          method: 'GET',
          timeout: 10000 // 10秒超时
        });

        if (!response.ok) {
          throw new Error(`代理API请求失败: ${response.status} ${response.statusText}`);
        }

        const proxyText = await response.text();
        console.log('🔍 代理API原始响应:', proxyText);
        console.log('🔍 响应长度:', proxyText.length);
        console.log('🔍 响应类型:', typeof proxyText);

        // 解析代理信息 (格式: IP:PORT USERNAME PASSWORD)
        const proxyParts = proxyText.trim().split(' ');
        console.log('🔍 代理信息分段数量:', proxyParts.length);
        console.log('🔍 代理信息分段详情:', proxyParts);

        let host, port, username, password;

        if (proxyParts.length >= 3) {
          // 格式: IP:PORT USERNAME PASSWORD (带认证)
          const [ipPort, user, pass] = proxyParts;
          const [ip, portStr] = ipPort.split(':');

          host = ip;
          port = portStr;
          username = user;
          password = pass;

          console.log('🔍 解析结果:');
          console.log('  - IP:', host);
          console.log('  - 端口:', port);
          console.log('  - 用户名:', username);
          console.log('  - 密码:', password ? '***' : '无');
          console.log('� 检测到SOCKS5认证代理格式');
        } else if (proxyParts.length === 1 && proxyParts[0].includes(':')) {
          // 格式: IP:PORT (匿名代理)
          const [ip, portStr] = proxyParts[0].split(':');

          host = ip;
          port = portStr;
          username = null;
          password = null;

          console.log('🔍 解析结果:');
          console.log('  - IP:', host);
          console.log('  - 端口:', port);
          console.log('  - 认证:', '无');
          console.log('📡 检测到SOCKS5匿名代理格式');
        } else {
          throw new Error(`代理格式错误，期望 IP:PORT USERNAME PASSWORD，实际: ${proxyText} (分段数量: ${proxyParts.length})`);
        }

        // 验证代理信息
        if (!host || !port) {
          throw new Error(`代理信息不完整: ${proxyText}`);
        }

        // 验证端口号是否为有效数字
        const portNumber = parseInt(port);
        if (isNaN(portNumber) || portNumber < 1 || portNumber > 65535) {
          throw new Error(`无效的端口号: ${port}`);
        }

        console.log(`📡 解析到SOCKS5认证代理: ${host}:${portNumber} (用户: ${username})`);

        // 首先尝试SOCKS5代理配置
        let proxyConfig = {
          mode: 'fixed_servers',
          rules: {
            singleProxy: {
              scheme: 'socks5',
              host: host,
              port: portNumber
            }
          }
        };

        console.log('🔧 尝试SOCKS5代理配置:', JSON.stringify(proxyConfig));

        console.log('🔧 配置代理设置:', JSON.stringify(proxyConfig, null, 2));

        // 设置代理
        if (!chrome.proxy) {
          throw new Error('Chrome代理API不可用');
        }

        await chrome.proxy.settings.set({
          value: proxyConfig,
          scope: 'regular'
        });

        console.log('✅ 代理设置已应用到Chrome');

        // 保存SOCKS5认证代理信息
        const proxyData = {
          host: host,
          port: portNumber,
          type: 'socks5_authenticated',
          protocol: 'socks5',
          username: username,
          password: password,
          timestamp: Date.now()
        };

        await chrome.storage.local.set({
          currentProxy: proxyData
        });

        console.log('💾 SOCKS5认证代理信息已保存到存储');
        console.log('✅ SOCKS5认证代理服务器配置完成:', `${host}:${portNumber} (用户: ${username})`);

        // 验证代理连接
        const connectionTest = await this.verifyProxyConnection(host, portNumber);

        sendResponse({
          success: true,
          data: {
            host: host,
            port: portNumber,
            type: 'socks5_authenticated',
            protocol: 'socks5',
            username: username,
            attempt: attempt,
            connectionTest: connectionTest,
            hasAuth: true
          }
        });
        return;

      } catch (error) {
        lastError = error;
        console.error(`❌ 代理切换失败 (尝试 ${attempt}/${maxRetries}):`, error.message);

        if (attempt < maxRetries) {
          console.log(`⏳ 等待 ${attempt * 2} 秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 2000));
        }
      }
    }

    // 所有尝试都失败了
    console.error('❌ 代理切换彻底失败，尝试回退到无代理模式');

    try {
      // 回退到无代理模式
      await chrome.proxy.settings.clear({ scope: 'regular' });
      console.log('✅ 已回退到无代理模式');
    } catch (clearError) {
      console.error('❌ 清除代理设置失败:', clearError);
    }

    sendResponse({
      success: false,
      error: `代理切换失败，已尝试 ${maxRetries} 次: ${lastError?.message || '未知错误'}`,
      fallbackToNoProxy: true
    });
  }



  /**
   * 验证代理连接
   */
  async verifyProxyConnection(host, port) {
    try {
      console.log(`🔍 开始验证SOCKS5代理连接: ${host}:${port}`);

      // 尝试通过代理访问一个简单的测试URL
      const testUrl = 'https://httpbin.org/ip';
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时

      console.log(`📡 通过SOCKS5代理测试连接到: ${testUrl}`);

      try {
        const response = await fetch(testUrl, {
          signal: controller.signal,
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const result = await response.json();
          console.log('✅ SOCKS5代理连接验证成功');
          console.log('🌐 当前IP地址:', result.origin);
          return {
            success: true,
            ip: result.origin,
            responseTime: Date.now()
          };
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (fetchError) {
        clearTimeout(timeoutId);

        if (fetchError.name === 'AbortError') {
          console.warn('⏰ SOCKS5代理连接测试超时');
          return {
            success: false,
            error: '连接超时',
            timeout: true
          };
        }

        console.warn('⚠️ SOCKS5代理连接测试失败:', fetchError.message);
        return {
          success: false,
          error: fetchError.message
        };
      }

    } catch (error) {
      console.warn('⚠️ 代理验证失败:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }



  /**
   * 获取系统状态
   */
  getSystemStatus() {
    return {
      isInitialized: this.isInitialized,
      isMonitoring: this.emailMonitor?.isCurrentlyMonitoring() || false,
      currentTabId: this.emailMonitor?.getCurrentTabId() || null,
      lastError: null // 可以添加错误跟踪
    };
  }

  /**
   * 获取当前代理设置
   */
  async handleGetProxySettings(sendResponse) {
    try {
      console.log('🔧 获取Chrome代理设置...');

      // 获取当前代理配置
      const proxySettings = await new Promise((resolve, reject) => {
        chrome.proxy.settings.get({}, (details) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(details);
          }
        });
      });

      console.log('🔧 Chrome代理配置:', proxySettings);

      // 获取存储的代理信息
      const storage = await chrome.storage.local.get(['currentProxy']);

      const result = {
        chromeSettings: proxySettings,
        storedProxy: storage.currentProxy || null,
        timestamp: Date.now()
      };

      console.log('🔧 代理设置获取成功:', result);

      sendResponse({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('❌ 获取代理设置失败:', error);
      sendResponse({
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 处理代理认证
   */
  async handleProxyAuth(details) {
    try {
      console.log('🔐 收到SOCKS5代理认证请求:', details.challenger);

      // 获取当前代理信息
      const storage = await chrome.storage.local.get(['currentProxy']);
      const proxyInfo = storage.currentProxy;

      if (!proxyInfo) {
        console.warn('⚠️ 未找到代理信息，无法提供认证');
        return { cancel: true };
      }

      if (proxyInfo.type === 'socks5_authenticated' && proxyInfo.username && proxyInfo.password) {
        console.log('🔐 提供SOCKS5代理认证信息:', proxyInfo.username);
        return {
          authCredentials: {
            username: proxyInfo.username,
            password: proxyInfo.password
          }
        };
      } else {
        console.warn('⚠️ 代理无认证信息');
        return { cancel: true };
      }

    } catch (error) {
      console.error('❌ SOCKS5代理认证处理失败:', error);
      return { cancel: true };
    }
  }

  /**
   * 在无痕模式下打开URL
   */
  async handleOpenInIncognito(data, sendResponse) {
    try {
      console.log('🔒 在无痕模式下打开:', data.url);

      const window = await chrome.windows.create({
        url: data.url,
        incognito: true,
        focused: true,
        type: 'normal',
        state: 'maximized'
      });

      console.log('✅ 无痕窗口已创建，窗口ID:', window.id);

      sendResponse({
        success: true,
        windowId: window.id,
        message: '无痕窗口已创建，准备开始下一次注册'
      });

    } catch (error) {
      console.error('❌ 创建无痕窗口失败:', error);
      sendResponse({
        success: false,
        error: error.message || '创建无痕窗口失败'
      });
    }
  }
}

// 创建Service Worker实例
const serviceWorker = new ServiceWorkerController();

// 导出供测试使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ServiceWorkerController };
}
